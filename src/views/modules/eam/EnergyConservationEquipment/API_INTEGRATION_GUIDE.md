# API接口对接指南

## 概述

本文档详细说明了网络设备监控系统的API接口设计和对接方式。当前系统使用模拟数据，按照本指南可以轻松替换为真实的后端接口。

## 接口设计原则

### 1. 统一响应格式
所有API接口都遵循统一的响应格式：

```typescript
interface ApiResponse<T = any> {
  code: number;      // HTTP状态码或业务状态码
  message: string;   // 响应消息
  data: T;          // 响应数据
  success: boolean; // 操作是否成功
}
```

### 2. RESTful设计
- 使用标准的HTTP方法（GET、POST、PUT、DELETE）
- 资源路径清晰明确
- 状态码语义化

### 3. 分页规范
列表接口统一使用以下分页格式：

```typescript
interface PaginationInfo {
  current: number;   // 当前页码
  pageSize: number;  // 每页大小
  total: number;     // 总记录数
}

interface DeviceListResponse<T> {
  list: T[];                    // 数据列表
  pagination: PaginationInfo;   // 分页信息
}
```

## 核心接口定义

### 1. 服务器设备接口

#### 1.1 获取服务器统计数据
```
GET /api/server-devices/statistics
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取成功",
  "success": true,
  "data": [
    {
      "title": "设备总数",
      "value": 156,
      "trend": {
        "type": "up",
        "value": 12,
        "period": "较上月"
      }
    }
  ]
}
```

#### 1.2 获取服务器趋势图表
```
GET /api/server-devices/trend-chart
```

#### 1.3 获取服务器设备列表
```
GET /api/server-devices/list?current=1&pageSize=20&searchKeyword=关键词
```

**请求参数：**
- `current`: 页码
- `pageSize`: 每页大小
- `searchKeyword`: 搜索关键词（可选）

### 2. 终端设备接口

#### 2.1 获取终端统计数据
```
GET /api/terminal-devices/statistics
```

#### 2.2 获取终端趋势图表
```
GET /api/terminal-devices/trend-chart
```

#### 2.3 获取终端设备列表
```
GET /api/terminal-devices/list?current=1&pageSize=20&searchKeyword=关键词
```

### 3. 设备操作接口

#### 3.1 获取设备详情
```
GET /api/devices/detail/{deviceId}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取成功",
  "success": true,
  "data": {
    "id": "device-001",
    "name": "服务器-001",
    "status": "online",
    "ipAddress": "*************",
    "macAddress": "00:1B:44:11:3A:B7",
    "assetOwner": "张三",
    "location": "机房A-01",
    "businessSystem": "核心业务系统",
    "manufacturer": "华为技术有限公司",
    "model": "RH2288H V3",
    "installDate": "2023-03-15",
    "warrantyEndDate": "2026-03-15",
    "description": "核心业务服务器",
    "ratedPower": 500,
    "currentPower": 320,
    "onlineTime": "2024-01-01 08:00:00",
    "totalOnlineHours": 8760,
    "lastMaintenanceDate": "2024-01-15",
    "nextMaintenanceDate": "2024-04-15",
    "healthScore": 95,
    "energyLevel": "A",
    "historyData": [
      {
        "date": "2024-01-01",
        "power": 310,
        "status": "online",
        "onlineHours": 24
      }
    ]
  }
}
```

#### 3.2 切换设备在线状态
```
POST /api/devices/toggle-status
```

**请求体：**
```json
{
  "deviceId": "device-001"
}
```

#### 3.3 移除设备
```
DELETE /api/devices/{deviceId}
```

## 数据类型定义

### 设备状态枚举
```typescript
enum DeviceStatus {
  ONLINE = 'online',      // 在线
  OFFLINE = 'offline',    // 离线
  FAULT = 'fault',        // 故障
  MAINTENANCE = 'maintenance' // 维护中
}
```

### 服务器设备数据结构
```typescript
interface ServerDevice {
  id: string;
  status: DeviceStatus;
  ipAddress: string;
  macAddress: string;
  assetOwner: string;
  location: string;
  businessSystem: string;    // 服务器特有字段
  onlineDuration: string;    // 在线时长
  operations: string[];      // 可执行操作
}
```

### 终端设备数据结构
```typescript
interface TerminalDevice {
  id: string;
  status: DeviceStatus;
  ipAddress: string;
  macAddress: string;
  assetOwner: string;
  location: string;
  nearestOnlineTime: string;    // 近期在线时间
  lastOnlineTime: string;       // 最后在线时间
  lastOnlineDuration: string;   // 最后在线时长
  operations: string[];         // 可执行操作
}
```

## 接口替换步骤

### 1. 修改API配置
在 `api/index.ts` 文件中，将模拟数据替换为真实的HTTP请求：

```typescript
// 替换前（模拟数据）
getStatistics: () => Promise.resolve({
  code: 200,
  message: '获取成功',
  data: serverStatistics,
  success: true
})

// 替换后（真实接口）
getStatistics: async () => {
  const response = await fetch('/api/server-devices/statistics');
  return await response.json();
}
```

### 2. 配置请求基础URL
建议在项目中配置统一的API基础URL：

```typescript
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || '/api';

export const request = async (url: string, options?: RequestInit) => {
  const response = await fetch(`${API_BASE_URL}${url}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers
    },
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
};
```

### 3. 错误处理
实现统一的错误处理机制：

```typescript
export const handleApiError = (error: any) => {
  console.error('API Error:', error);
  
  if (error.code === 401) {
    // 处理未授权
    router.push('/login');
  } else if (error.code === 403) {
    // 处理权限不足
    ElMessage.error('权限不足');
  } else {
    // 处理其他错误
    ElMessage.error(error.message || '请求失败');
  }
};
```

## 环境配置

### 开发环境
```bash
# .env.development
VUE_APP_API_BASE_URL=http://localhost:8080/api
```

### 生产环境
```bash
# .env.production
VUE_APP_API_BASE_URL=/api
```

## 测试建议

### 1. 接口测试
- 使用Postman或类似工具测试所有接口
- 验证请求参数和响应格式
- 测试异常情况处理

### 2. 集成测试
- 在开发环境中逐步替换接口
- 验证数据流转正确性
- 测试用户交互功能

### 3. 性能测试
- 测试大数据量下的响应时间
- 验证分页功能性能
- 监控内存使用情况

## 注意事项

1. **数据格式一致性**: 确保后端返回的数据格式与前端类型定义一致
2. **错误处理**: 实现完善的错误处理和用户提示
3. **加载状态**: 在数据请求过程中显示适当的加载状态
4. **缓存策略**: 考虑实现适当的数据缓存机制
5. **安全性**: 确保API接口的安全性，包括认证和授权

## 联系方式

如有接口对接相关问题，请联系开发团队。
