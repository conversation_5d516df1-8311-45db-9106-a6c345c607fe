# 网络设备监控系统 - 接口对接指南

## 项目概述

本项目实现了一个网络设备监控管理系统，严格按照原型图进行一比一还原，包含服务器设备和终端设备两个Tab的切换功能。

## 核心功能

### 1. Tab切换功能
- **服务器设备Tab**: 显示服务器相关的监控数据和设备列表
- **终端设备Tab**: 显示终端设备相关的监控数据和设备列表
- 每个Tab都有独立的统计卡片、趋势图表和设备列表

### 2. 数据管理架构

采用**领域驱动的数据管理方案**，具有以下优势：
- 领域分离：每个Tab对应独立的业务领域
- 类型安全：TypeScript接口定义精确
- 可维护性：业务逻辑分离，便于独立开发
- 扩展性：新增Tab时不影响现有代码
- 接口对接：每个领域独立的API层

## 文件结构

```
src/views/modules/eam/EnergyConservationEquipment/
├── index.vue                          # 主入口组件
├── components/
│   ├── NetworkDeviceHomePage.vue      # 主页面组件
│   └── NetworkDeviceDetails.vue       # 详情页面组件
├── types/
│   └── index.ts                       # TypeScript类型定义
├── mock/
│   ├── serverDeviceData.ts           # 服务器设备模拟数据
│   └── terminalDeviceData.ts         # 终端设备模拟数据
├── api/
│   └── index.ts                       # API服务层
└── API_INTEGRATION_GUIDE.md          # 本文档
```

## 接口对接步骤

### 第一步：了解数据结构

查看 `types/index.ts` 文件，了解所有的数据接口定义：

- `StatisticCard`: 统计卡片数据结构
- `TrendChartConfig`: 趋势图表配置结构
- `ServerDevice`: 服务器设备数据结构
- `TerminalDevice`: 终端设备数据结构
- `ApiResponse`: API响应基础结构

### 第二步：替换API实现

在 `api/index.ts` 文件中，当前使用的是模拟数据。需要替换以下API：

#### 服务器设备API
```typescript
export const serverDeviceApi = {
  // 替换为真实API调用
  async getStatistics(): Promise<ApiResponse<StatisticCard[]>> {
    // 替换为: return http.get('/api/server/statistics');
  },
  
  async getTrendChart(): Promise<ApiResponse<TrendChartConfig>> {
    // 替换为: return http.get('/api/server/trend-chart');
  },
  
  async getDeviceList(filters, pagination): Promise<ApiResponse<DeviceListResponse<ServerDevice>>> {
    // 替换为: return http.post('/api/server/device-list', { filters, pagination });
  }
};
```

#### 终端设备API
```typescript
export const terminalDeviceApi = {
  // 类似服务器设备API，替换为对应的终端设备接口
  async getStatistics(): Promise<ApiResponse<StatisticCard[]>> {
    // 替换为: return http.get('/api/terminal/statistics');
  },
  
  async getTrendChart(): Promise<ApiResponse<TrendChartConfig>> {
    // 替换为: return http.get('/api/terminal/trend-chart');
  },
  
  async getDeviceList(filters, pagination): Promise<ApiResponse<DeviceListResponse<TerminalDevice>>> {
    // 替换为: return http.post('/api/terminal/device-list', { filters, pagination });
  }
};
```

### 第三步：字段映射处理

如果后端接口返回的字段名与前端定义不一致，可以在API层进行字段映射：

```typescript
// 示例：字段映射函数
const mapServerDeviceData = (backendData: any): ServerDevice => {
  return {
    id: backendData.deviceId,
    status: backendData.deviceStatus,
    ipAddress: backendData.ip,
    macAddress: backendData.mac,
    businessSystem: backendData.businessSys,
    assetOwner: backendData.owner,
    location: backendData.position,
    onlineDuration: backendData.uptime,
    operations: backendData.availableOps || ['查看', '在线状态', '移除']
  };
};
```

### 第四步：错误处理

在API调用中添加适当的错误处理：

```typescript
async getDeviceList(filters: TableFilters, pagination: PaginationInfo) {
  try {
    const response = await http.post('/api/server/device-list', { filters, pagination });
    
    // 检查响应格式
    if (!response.success) {
      throw new Error(response.message || '请求失败');
    }
    
    // 数据映射（如果需要）
    const mappedData = response.data.list.map(mapServerDeviceData);
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        list: mappedData,
        pagination: response.data.pagination
      },
      success: true
    };
  } catch (error) {
    console.error('获取设备列表失败:', error);
    throw error;
  }
}
```

## 配置说明

### 1. HTTP配置

确保在 `src/utils/http/serverNames.ts` 中配置了正确的服务名：

```typescript
export const ServerNames = {
  networkDeviceServer: '/api/network-device'  // 添加网络设备服务配置
};
```

### 2. 路由配置

在路由配置中添加对应的路由规则，确保页面可以正常访问。

## 测试建议

1. **单元测试**: 为API服务层编写单元测试
2. **集成测试**: 测试前后端数据交互
3. **UI测试**: 验证Tab切换和数据显示功能
4. **响应式测试**: 测试不同屏幕尺寸下的显示效果

## 注意事项

1. **数据一致性**: 确保服务器设备和终端设备的数据结构与类型定义一致
2. **错误处理**: 添加适当的错误提示和加载状态
3. **性能优化**: 考虑数据缓存和分页加载
4. **安全性**: 确保API调用的安全性和权限控制

## 后续扩展

1. **新增设备类型**: 可以轻松添加新的设备Tab
2. **实时监控**: 可以集成WebSocket实现实时数据更新
3. **数据导出**: 可以添加数据导出功能
4. **高级筛选**: 可以扩展更多的筛选条件

## Tab切换实现逻辑

### 状态管理
```typescript
const state = reactive<HomePageState>({
  activeTab: DeviceTabType.SERVER,  // 当前激活的Tab
  serverDevices: [],               // 服务器设备列表
  terminalDevices: [],            // 终端设备列表
  // ... 其他状态
});
```

### 切换逻辑
```typescript
const switchTab = async (tabKey: DeviceTabType) => {
  state.activeTab = tabKey;
  await loadDeviceList();  // 加载对应Tab的数据
  await nextTick();
  initChart();            // 初始化对应的图表
};
```

### 数据获取
```typescript
const currentStatistics = computed(() => {
  return state.activeTab === DeviceTabType.SERVER 
    ? serverStatistics.value 
    : terminalStatistics.value;
});
```

## 联系方式

如有问题，请联系开发团队或查看相关技术文档。
