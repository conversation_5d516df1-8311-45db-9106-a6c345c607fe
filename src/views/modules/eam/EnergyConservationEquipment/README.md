# 网络设备监控系统

## 功能概述

网络设备监控系统是一个用于监控和管理网络设备（服务器设备和终端设备）的综合管理平台。系统提供设备状态监控、统计分析、趋势图表、设备详情查看等功能。

## 主要功能

### 1. 设备分类管理
- **服务器设备**: 监控服务器的运行状态、业务系统归属、在线时长等
- **终端设备**: 监控终端设备的在线状态、最近在线时间、在线时长等

### 2. 统计数据展示
- 设备总数统计
- 在线设备数量
- 离线设备数量
- 故障设备数量
- 超7天未在线设备数量

### 3. 趋势图表分析
- 设备在线趋势图
- 支持多维度筛选（平台、位置、时间范围）
- 实时数据更新

### 4. 设备详情查看
- **弹窗式详情展示**: 点击"查看"操作时弹出详情弹窗
- **基本信息**: 设备名称、状态、IP地址、MAC地址、责任人、位置等
- **设备规格**: 制造商、型号、功率、能效等级、安装日期等
- **运行状态**: 在线时间、离线时间、累计在线时长、维护信息等
- **历史数据**: 近7天功耗趋势图表
- **健康评分**: 设备健康度评估

### 5. 设备操作
- 查看设备详情
- 切换设备在线状态
- 移除设备

## 技术特点

### 1. 响应式设计
- 支持不同屏幕尺寸自适应
- 移动端友好的交互体验

### 2. 组件化架构
- 模块化设计，便于维护和扩展
- 组件状态切换，避免页面跳转

### 3. 数据可视化
- ECharts图表集成
- 多种图表类型支持
- 交互式数据展示

### 4. 接口设计
- 标准化API接口设计
- 便于后续真实接口对接
- 完善的错误处理机制

## 使用说明

### 1. 页面布局
- **左侧**: 组织架构树，支持搜索和筛选
- **右侧**: 主要内容区域，包含Tab切换、统计卡片、图表、设备列表

### 2. Tab切换
- **服务器设备**: 显示服务器相关信息和操作
- **终端设备**: 显示终端设备相关信息和操作

### 3. 设备详情弹窗
- 点击设备列表中的"查看"按钮打开详情弹窗
- 弹窗包含完整的设备信息和历史数据图表
- 支持编辑设备信息（功能待开发）

### 4. 数据筛选
- 支持关键词搜索
- 支持多维度筛选条件
- 实时更新筛选结果

## 接口对接指南

### API结构
所有API接口遵循统一的响应格式：
```typescript
interface ApiResponse<T> {
  code: number;      // 状态码
  message: string;   // 响应消息
  data: T;          // 响应数据
  success: boolean; // 是否成功
}
```

### 主要接口
1. **获取统计数据**: `GET /api/devices/statistics`
2. **获取趋势图表**: `GET /api/devices/trend-chart`
3. **获取设备列表**: `GET /api/devices/list`
4. **获取设备详情**: `GET /api/devices/detail/{id}`
5. **设备操作**: `POST /api/devices/operation`

### 数据格式
详细的数据格式定义请参考 `types/index.ts` 文件。

## 开发说明

### 文件结构
```
EnergyConservationEquipment/
├── index.vue                    # 主入口组件
├── components/
│   ├── NetworkDeviceHomePage.vue   # 主页面组件
│   └── NetworkDeviceDetails.vue    # 详情页面组件
├── types/
│   └── index.ts                 # 类型定义
├── api/
│   └── index.ts                 # API接口
├── mock/
│   ├── serverDeviceData.ts      # 服务器设备模拟数据
│   └── terminalDeviceData.ts    # 终端设备模拟数据
└── README.md                    # 说明文档
```

### 核心组件
- **NetworkDeviceHomePage**: 主页面组件，包含所有主要功能
- **设备详情弹窗**: 集成在主页面中的弹窗组件

### 状态管理
使用Vue 3的响应式API进行状态管理，主要状态包括：
- 当前激活的Tab
- 设备列表数据
- 筛选条件
- 分页信息
- 加载状态

## 后续开发计划

1. **设备编辑功能**: 完善设备信息编辑功能
2. **批量操作**: 支持批量设备操作
3. **导出功能**: 支持数据导出
4. **实时监控**: 集成WebSocket实现实时数据更新
5. **告警系统**: 设备异常告警功能
6. **权限管理**: 基于角色的权限控制

## 注意事项

1. 当前使用模拟数据，实际部署时需要替换为真实API接口
2. 图表组件依赖ECharts，确保正确引入相关依赖
3. 响应式布局在小屏幕设备上可能需要进一步优化
4. 设备详情弹窗的历史数据图表需要真实数据支持
