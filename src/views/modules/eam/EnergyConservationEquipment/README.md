# 节能设备管理模块

## 概述

节能设备管理模块是一个完整的企业级设备管理系统，提供设备信息管理、能耗监控、维护记录等功能。该模块采用Vue 3 + TypeScript + Element Plus技术栈开发，遵循企业级代码规范和设计模式。

## 功能特性

### 🏠 主页面功能
- **左右分栏布局**：左侧树形导航，右侧内容区域
- **双视图切换**：设备分类视图和组织视图
- **智能搜索**：支持多字段模糊搜索和精确搜索
- **状态筛选**：设备状态和类型标签筛选
- **数据统计**：实时显示设备统计信息
- **批量操作**：支持批量导出、删除等操作

### 📊 表格功能
- **动态列配置**：支持列显示/隐藏、拖拽排序
- **列头过滤**：每列支持独立过滤条件
- **数据分页**：支持分页查询和页面大小调整
- **响应式设计**：适配不同屏幕尺寸
- **数据导出**：支持Excel格式导出

### 📋 详情页面
- **基本信息**：设备完整信息展示
- **能耗统计**：实时能耗数据和趋势图表
- **维护记录**：设备维护历史记录
- **操作日志**：设备操作审计日志

## 文件结构

```
EnergyConservationEquipment/
├── index.vue                    # 主入口文件，组件切换控制
├── components/                  # 组件目录
│   ├── EnergyEquipmentHomePage.vue    # 主页面组件
│   └── EnergyEquipmentDetails.vue     # 详情页面组件
├── api/                        # API接口目录
│   └── index.ts               # 接口定义文件
├── types/                     # 类型定义目录
│   └── index.ts              # TypeScript类型定义
├── utils/                     # 工具方法目录
│   └── index.ts              # 工具函数和枚举
├── mock/                      # Mock数据目录
│   └── index.ts              # 测试数据
└── README.md                  # 说明文档
```

## 技术栈

- **Vue 3**: 使用Composition API
- **TypeScript**: 完整的类型定义
- **Element Plus**: UI组件库
- **ImTable**: 企业级表格组件
- **Splitpane**: 分栏布局组件
- **dayjs**: 日期处理库

## 设计模式

### 1. 组件状态切换模式
- 使用组件内部状态切换而非路由跳转
- 通过`currentComponent`动态切换显示组件
- 保持组件间数据状态和用户体验流畅性

### 2. 数据驱动模式
- 所有UI状态通过响应式数据驱动
- 统一的数据管理和状态更新
- 清晰的数据流向和状态管理

### 3. 配置化模式
- 表格列配置化定义
- 筛选条件配置化管理
- 可扩展的配置项设计

## 使用方法

### 1. 基本使用

```vue
<template>
  <EnergyConservationEquipment />
</template>

<script setup>
import EnergyConservationEquipment from '@/views/modules/eam/EnergyConservationEquipment/index.vue'
</script>
```

### 2. API接口配置

在`api/index.ts`中配置后端接口地址：

```typescript
// 修改服务器名称
const basePath = `${ServerNames.eamCoreServer}`;

// 修改接口路径
const pathList = {
  equipmentListUrl: "/energy/equipment/list",
  // ... 其他接口
};
```

### 3. Mock数据测试

开发阶段可以使用Mock数据进行测试：

```typescript
import { mockEquipmentListData } from './mock/index';

// 在API方法中返回Mock数据
export const queryEnergyEquipmentList = (params: any) => {
  return Promise.resolve(mockEquipmentListData);
};
```

## 核心组件说明

### EnergyEquipmentHomePage.vue
主页面组件，包含以下核心功能：
- 左侧树形导航（设备分类/组织视图）
- 右侧搜索和筛选区域
- 数据表格展示
- 批量操作功能

### EnergyEquipmentDetails.vue
详情页面组件，包含以下功能：
- 设备基本信息展示
- 能耗数据统计
- 维护记录时间线
- 操作日志表格

## 扩展指南

### 1. 添加新的筛选条件

在`EnergyEquipmentHomePage.vue`中添加新的筛选标签：

```typescript
// 添加新的筛选数据
const newFilterData = [
  { label: "新筛选项1", value: "value1", checked: false },
  { label: "新筛选项2", value: "value2", checked: false }
];

// 添加筛选处理方法
const newFilterChangeHandler = (tag: any) => {
  // 处理筛选逻辑
};
```

### 2. 添加新的表格列

在`tableColumns`计算属性中添加新列：

```typescript
{
  label: "新列名",
  prop: "newProperty",
  width: "120px",
  align: "center",
  filterable: true,
  sortable: true
}
```

### 3. 添加新的操作按钮

在表格操作列模板中添加新按钮：

```vue
<template #operator="{ row, size }">
  <!-- 现有按钮 -->
  <el-button 
    :size="size" 
    type="success" 
    text 
    @click="newOperationHandler(row)"
  >
    新操作
  </el-button>
</template>
```

## 注意事项

1. **数据格式**：确保后端返回的数据格式与类型定义一致
2. **权限控制**：根据用户权限控制操作按钮的显示和功能
3. **错误处理**：完善API调用的错误处理和用户提示
4. **性能优化**：大数据量时考虑虚拟滚动和分页加载
5. **响应式设计**：确保在不同设备上的良好显示效果

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 完整的设备管理功能
- 支持设备分类和组织视图
- 实现详情页面和操作功能
- 完善的类型定义和工具方法

## 贡献指南

1. 遵循项目的代码规范和命名约定
2. 添加新功能时更新相应的类型定义
3. 编写清晰的注释和文档
4. 确保代码的可维护性和扩展性
5. 提交前进行充分的测试

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。
