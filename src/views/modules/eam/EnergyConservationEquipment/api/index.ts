/**
 * 网络设备监控API服务层
 * 提供统一的数据接口，便于后续真实API对接
 */
import {
  ApiResponse,
  DeviceListResponse,
  ServerDevice,
  TerminalDevice,
  StatisticCard,
  TrendChartConfig,
  TableFilters,
  PaginationInfo
} from '../types';

// 模拟数据导入
import {
  serverStatistics,
  serverTrendChart,
  serverDeviceList
} from '../mock/serverDeviceData';

import {
  terminalStatistics,
  terminalTrendChart,
  terminalDeviceList
} from '../mock/terminalDeviceData';

/**
 * 模拟API请求延迟
 */
const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 服务器设备相关API
 */
export const serverDeviceApi = {
  /**
   * 获取服务器设备统计数据
   */
  async getStatistics(): Promise<ApiResponse<StatisticCard[]>> {
    await mockDelay();
    return {
      code: 200,
      message: '获取成功',
      data: serverStatistics,
      success: true
    };
  },

  /**
   * 获取服务器设备趋势图表配置
   */
  async getTrendChart(): Promise<ApiResponse<TrendChartConfig>> {
    await mockDelay();
    return {
      code: 200,
      message: '获取成功',
      data: serverTrendChart,
      success: true
    };
  },

  /**
   * 获取服务器设备列表
   */
  async getDeviceList(
    filters: TableFilters,
    pagination: PaginationInfo
  ): Promise<ApiResponse<DeviceListResponse<ServerDevice>>> {
    await mockDelay();

    // 模拟筛选和分页逻辑
    let filteredList = [...serverDeviceList];

    // 搜索筛选
    if (filters.searchKeyword) {
      filteredList = filteredList.filter(device =>
        device.ipAddress.includes(filters.searchKeyword) ||
        device.assetOwner.includes(filters.searchKeyword) ||
        device.location.includes(filters.searchKeyword)
      );
    }

    const total = filteredList.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    const list = filteredList.slice(startIndex, endIndex);

    return {
      code: 200,
      message: '获取成功',
      data: {
        list,
        pagination: {
          ...pagination,
          total
        }
      },
      success: true
    };
  }
};

/**
 * 终端设备相关API
 */
export const terminalDeviceApi = {
  /**
   * 获取终端设备统计数据
   */
  async getStatistics(): Promise<ApiResponse<StatisticCard[]>> {
    await mockDelay();
    return {
      code: 200,
      message: '获取成功',
      data: terminalStatistics,
      success: true
    };
  },

  /**
   * 获取终端设备趋势图表配置
   */
  async getTrendChart(): Promise<ApiResponse<TrendChartConfig>> {
    await mockDelay();
    return {
      code: 200,
      message: '获取成功',
      data: terminalTrendChart,
      success: true
    };
  },

  /**
   * 获取终端设备列表
   */
  async getDeviceList(
    filters: TableFilters,
    pagination: PaginationInfo
  ): Promise<ApiResponse<DeviceListResponse<TerminalDevice>>> {
    await mockDelay();

    // 模拟筛选和分页逻辑
    let filteredList = [...terminalDeviceList];

    // 搜索筛选
    if (filters.searchKeyword) {
      filteredList = filteredList.filter(device =>
        device.ipAddress.includes(filters.searchKeyword) ||
        device.assetOwner.includes(filters.searchKeyword) ||
        device.location.includes(filters.searchKeyword)
      );
    }

    const total = filteredList.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    const list = filteredList.slice(startIndex, endIndex);

    return {
      code: 200,
      message: '获取成功',
      data: {
        list,
        pagination: {
          ...pagination,
          total
        }
      },
      success: true
    };
  }
};

/**
 * 设备操作相关API
 */
export const deviceOperationApi = {
  /**
   * 查看设备详情
   */
  async viewDevice(deviceId: string): Promise<ApiResponse<any>> {
    await mockDelay(300);
    return {
      code: 200,
      message: '操作成功',
      data: { deviceId },
      success: true
    };
  },

  /**
   * 切换设备在线状态
   */
  async toggleOnlineStatus(deviceId: string): Promise<ApiResponse<any>> {
    await mockDelay(800);
    return {
      code: 200,
      message: '状态切换成功',
      data: { deviceId },
      success: true
    };
  },

  /**
   * 移除设备
   */
  async removeDevice(deviceId: string): Promise<ApiResponse<any>> {
    await mockDelay(1000);
    return {
      code: 200,
      message: '设备移除成功',
      data: { deviceId },
      success: true
    };
  }
};

// 导出统一的API对象，便于后续替换为真实API
export default {
  server: serverDeviceApi,
  terminal: terminalDeviceApi,
  operation: deviceOperationApi
};
