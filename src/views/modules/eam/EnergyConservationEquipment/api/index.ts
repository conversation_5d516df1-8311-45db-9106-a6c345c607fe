/**
 * 网络设备监控API服务层
 */
import {
  ApiResponse,
  DeviceListResponse,
  ServerDevice,
  TerminalDevice,
  StatisticCard,
  TrendChartConfig,
  TableFilters,
  PaginationInfo
} from '../types';

// 模拟数据导入
import {
  serverStatistics,
  serverTrendChart,
  serverDeviceList
} from '../mock/serverDeviceData';

import {
  terminalStatistics,
  terminalTrendChart,
  terminalDeviceList
} from '../mock/terminalDeviceData';

// 服务器设备API
export const serverDeviceApi = {
  getStatistics: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: serverStatistics,
    success: true
  }),

  getTrendChart: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: serverTrendChart,
    success: true
  }),

  getDeviceList: (filters: TableFilters, pagination: PaginationInfo) => {
    let filteredList = [...serverDeviceList];

    if (filters.searchKeyword) {
      filteredList = filteredList.filter(device =>
        device.ipAddress.includes(filters.searchKeyword) ||
        device.assetOwner.includes(filters.searchKeyword) ||
        device.location.includes(filters.searchKeyword)
      );
    }

    const total = filteredList.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const list = filteredList.slice(startIndex, startIndex + pagination.pageSize);

    return Promise.resolve({
      code: 200,
      message: '获取成功',
      data: { list, pagination: { ...pagination, total } },
      success: true
    });
  }
};

// 终端设备API
export const terminalDeviceApi = {
  getStatistics: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: terminalStatistics,
    success: true
  }),

  getTrendChart: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: terminalTrendChart,
    success: true
  }),

  getDeviceList: (filters: TableFilters, pagination: PaginationInfo) => {
    let filteredList = [...terminalDeviceList];

    if (filters.searchKeyword) {
      filteredList = filteredList.filter(device =>
        device.ipAddress.includes(filters.searchKeyword) ||
        device.assetOwner.includes(filters.searchKeyword) ||
        device.location.includes(filters.searchKeyword)
      );
    }

    const total = filteredList.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const list = filteredList.slice(startIndex, startIndex + pagination.pageSize);

    return Promise.resolve({
      code: 200,
      message: '获取成功',
      data: { list, pagination: { ...pagination, total } },
      success: true
    });
  }
};

// 设备操作API
export const deviceOperationApi = {
  viewDevice: (deviceId: string) => Promise.resolve({
    code: 200,
    message: '操作成功',
    data: { deviceId },
    success: true
  }),

  toggleOnlineStatus: (deviceId: string) => Promise.resolve({
    code: 200,
    message: '状态切换成功',
    data: { deviceId },
    success: true
  }),

  removeDevice: (deviceId: string) => Promise.resolve({
    code: 200,
    message: '设备移除成功',
    data: { deviceId },
    success: true
  })
};

// 统一导出
export default {
  server: serverDeviceApi,
  terminal: terminalDeviceApi,
  operation: deviceOperationApi
};
