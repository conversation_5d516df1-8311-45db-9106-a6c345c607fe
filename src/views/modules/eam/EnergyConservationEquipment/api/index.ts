/**
 * 网络设备监控API服务层
 */
import {
  ApiResponse,
  DeviceListResponse,
  ServerDevice,
  TerminalDevice,
  StatisticCard,
  TrendChartConfig,
  TableFilters,
  PaginationInfo
} from '../types';

// 模拟数据导入
import {
  serverStatistics,
  serverTrendChart,
  serverDeviceList
} from '../mock/serverDeviceData';

import {
  terminalStatistics,
  terminalTrendChart,
  terminalDeviceList
} from '../mock/terminalDeviceData';

// 服务器设备API
export const serverDeviceApi = {
  getStatistics: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: serverStatistics,
    success: true
  }),

  getTrendChart: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: serverTrendChart,
    success: true
  }),

  getDeviceList: (filters: TableFilters, pagination: PaginationInfo) => {
    let filteredList = [...serverDeviceList];

    if (filters.searchKeyword) {
      filteredList = filteredList.filter(device =>
        device.ipAddress.includes(filters.searchKeyword) ||
        device.assetOwner.includes(filters.searchKeyword) ||
        device.location.includes(filters.searchKeyword)
      );
    }

    const total = filteredList.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const list = filteredList.slice(startIndex, startIndex + pagination.pageSize);

    return Promise.resolve({
      code: 200,
      message: '获取成功',
      data: { list, pagination: { ...pagination, total } },
      success: true
    });
  }
};

// 终端设备API
export const terminalDeviceApi = {
  getStatistics: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: terminalStatistics,
    success: true
  }),

  getTrendChart: () => Promise.resolve({
    code: 200,
    message: '获取成功',
    data: terminalTrendChart,
    success: true
  }),

  getDeviceList: (filters: TableFilters, pagination: PaginationInfo) => {
    let filteredList = [...terminalDeviceList];

    if (filters.searchKeyword) {
      filteredList = filteredList.filter(device =>
        device.ipAddress.includes(filters.searchKeyword) ||
        device.assetOwner.includes(filters.searchKeyword) ||
        device.location.includes(filters.searchKeyword)
      );
    }

    const total = filteredList.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const list = filteredList.slice(startIndex, startIndex + pagination.pageSize);

    return Promise.resolve({
      code: 200,
      message: '获取成功',
      data: { list, pagination: { ...pagination, total } },
      success: true
    });
  }
};

// 设备操作API
export const deviceOperationApi = {
  getDeviceDetail: (deviceId: string) => {
    // 模拟设备详情数据
    const mockDetail = {
      id: deviceId,
      name: `设备-${deviceId}`,
      status: 'online',
      ipAddress: '*************',
      macAddress: '00:1B:44:11:3A:B7',
      assetOwner: '张三',
      location: '机房A-01',
      businessSystem: '核心业务系统',
      manufacturer: '华为技术有限公司',
      model: 'RH2288H V3',
      installDate: '2023-03-15',
      warrantyEndDate: '2026-03-15',
      description: '核心业务服务器，负责处理主要业务逻辑',
      ratedPower: 500,
      currentPower: 320,
      onlineTime: '2024-01-01 08:00:00',
      offlineTime: '',
      totalOnlineHours: 8760,
      lastMaintenanceDate: '2024-01-15',
      nextMaintenanceDate: '2024-04-15',
      healthScore: 95,
      energyLevel: 'A',
      historyData: [
        { date: '2024-01-01', power: 310, status: 'online', onlineHours: 24 },
        { date: '2024-01-02', power: 320, status: 'online', onlineHours: 24 },
        { date: '2024-01-03', power: 315, status: 'online', onlineHours: 22 },
        { date: '2024-01-04', power: 325, status: 'online', onlineHours: 24 },
        { date: '2024-01-05', power: 318, status: 'online', onlineHours: 24 },
        { date: '2024-01-06', power: 312, status: 'online', onlineHours: 20 },
        { date: '2024-01-07', power: 322, status: 'online', onlineHours: 24 }
      ]
    };

    return Promise.resolve({
      code: 200,
      message: '获取成功',
      data: mockDetail,
      success: true
    });
  },

  toggleOnlineStatus: (deviceId: string) => Promise.resolve({
    code: 200,
    message: '状态切换成功',
    data: { deviceId },
    success: true
  }),

  removeDevice: (deviceId: string) => Promise.resolve({
    code: 200,
    message: '设备移除成功',
    data: { deviceId },
    success: true
  })
};

// 统一导出
export default {
  server: serverDeviceApi,
  terminal: terminalDeviceApi,
  operation: deviceOperationApi
};
