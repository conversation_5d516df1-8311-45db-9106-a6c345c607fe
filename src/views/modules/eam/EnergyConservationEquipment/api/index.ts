import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}`;
const basePath02 = `${ServerNames.eamCoreServer}`;
const basePath03 = `/framework/sysmanage`;

const pathList = {
  // 节能设备相关接口路径
  equipmentListUrl: "/energy/equipment/list", // 设备列表数据
  equipmentDetailUrl: "/energy/equipment/detail", // 设备详情数据
  equipmentCategoryUrl: "/energy/equipment/category", // 设备分类树数据
  equipmentSearchUrl: "/energy/equipment/search", // 设备搜索
  equipmentExportUrl: "/energy/equipment/export", // 设备数据导出
  equipmentImportUrl: "/energy/equipment/import", // 设备数据导入
  equipmentSaveUrl: "/energy/equipment/save", // 保存设备信息
  equipmentUpdateUrl: "/energy/equipment/update", // 更新设备信息
  equipmentDeleteUrl: "/energy/equipment/delete", // 删除设备
  equipmentStatusUrl: "/energy/equipment/status", // 设备状态管理
  
  // 组织架构相关
  getUserDeptTreeUrl: "/dept/getUserDeptTree", // 得到所属组织下拉树
  queryAllCategoryUrl: "/category/getAllCategory", // 得到资产类别下拉树
};

/**
 * 查询节能设备列表数据
 * @param params 查询参数
 */
export const queryEnergyEquipmentList = (params: any) => {
  return http.postJson(`${basePath}${pathList.equipmentListUrl}`, params);
};

/**
 * 查询节能设备详情
 * @param equipmentId 设备ID
 */
export const queryEnergyEquipmentDetail = (equipmentId: string) => {
  return http.get(`${basePath}${pathList.equipmentDetailUrl}`, { equipmentId });
};

/**
 * 查询设备分类树数据
 */
export const queryEquipmentCategoryTree = () => {
  return http.get(`${basePath}${pathList.equipmentCategoryUrl}`);
};

/**
 * 搜索节能设备
 * @param params 搜索参数
 */
export const searchEnergyEquipment = (params: any) => {
  return http.postJson(`${basePath}${pathList.equipmentSearchUrl}`, params);
};

/**
 * 导出节能设备数据
 * @param params 导出参数
 */
export const exportEnergyEquipmentData = (params: any) => {
  return http.postBlobWithJson(`${basePath}${pathList.equipmentExportUrl}`, params);
};

/**
 * 保存节能设备信息
 * @param params 设备信息
 */
export const saveEnergyEquipment = (params: any) => {
  return http.postJson(`${basePath}${pathList.equipmentSaveUrl}`, params);
};

/**
 * 更新节能设备信息
 * @param params 设备信息
 */
export const updateEnergyEquipment = (params: any) => {
  return http.postJson(`${basePath}${pathList.equipmentUpdateUrl}`, params);
};

/**
 * 删除节能设备
 * @param equipmentIds 设备ID数组
 */
export const deleteEnergyEquipment = (equipmentIds: string[]) => {
  return http.postJson(`${basePath}${pathList.equipmentDeleteUrl}`, { equipmentIds });
};

/**
 * 更新设备状态
 * @param params 状态更新参数
 */
export const updateEquipmentStatus = (params: any) => {
  return http.postJson(`${basePath}${pathList.equipmentStatusUrl}`, params);
};

/**
 * 获取组织架构树数据
 */
export const getUserDeptTreeAxios = () => {
  return http.get(`${basePath03}${pathList.getUserDeptTreeUrl}`);
};

/**
 * 获取资产类别数据
 */
export const queryAllCategoryAxios = () => {
  return http.get(`${basePath02}${pathList.queryAllCategoryUrl}`);
};

/**
 * 查询设备统计数据
 * @param params 查询参数
 */
export const queryEquipmentStatistics = (params: any) => {
  return http.postJson(`${basePath}/energy/equipment/statistics`, params);
};

/**
 * 查询设备能耗数据
 * @param params 查询参数
 */
export const queryEquipmentEnergyConsumption = (params: any) => {
  return http.postJson(`${basePath}/energy/equipment/consumption`, params);
};

/**
 * 查询设备维护记录
 * @param equipmentId 设备ID
 */
export const queryEquipmentMaintenanceRecords = (equipmentId: string) => {
  return http.get(`${basePath}/energy/equipment/maintenance`, { equipmentId });
};
