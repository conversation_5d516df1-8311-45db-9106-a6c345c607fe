<template>
  <div class="energy-equipment-details">
    <!-- 头部导航 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button
          type="primary"
          :icon="useRenderIcon('EP-ArrowLeft')"
          @click="handleBack"
        >
          返回列表
        </el-button>
        <div class="equipment-title">
          <h2>{{ equipmentInfo.equipmentName || '设备详情' }}</h2>
          <el-tag
            :type="getStatusType(equipmentInfo.status)"
            effect="light"
            size="large"
          >
            {{ getStatusLabel(equipmentInfo.status) }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button
          type="warning"
          :icon="useRenderIcon('EP-Edit')"
          @click="handleEdit"
        >
          编辑设备
        </el-button>
        <el-button
          type="danger"
          :icon="useRenderIcon('EP-Delete')"
          @click="handleDelete"
        >
          删除设备
        </el-button>
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content">
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-section">
            <div class="section-title">
              <IconifyIconOffline icon="RI-InformationLine" />
              <span>基本信息</span>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <label>设备名称：</label>
                <span>{{ equipmentInfo.equipmentName || '-' }}</span>
              </div>
              <div class="info-item">
                <label>设备编号：</label>
                <span>{{ equipmentInfo.equipmentCode || '-' }}</span>
              </div>
              <div class="info-item">
                <label>设备类型：</label>
                <el-tag>{{ equipmentInfo.equipmentType || '-' }}</el-tag>
              </div>
              <div class="info-item">
                <label>设备分类：</label>
                <span>{{ equipmentInfo.categoryName || '-' }}</span>
              </div>
              <div class="info-item">
                <label>所属组织：</label>
                <span>{{ equipmentInfo.deptName || '-' }}</span>
              </div>
              <div class="info-item">
                <label>安装位置：</label>
                <span>{{ equipmentInfo.location || '-' }}</span>
              </div>
              <div class="info-item">
                <label>设备状态：</label>
                <el-tag :type="getStatusType(equipmentInfo.status)" effect="light">
                  {{ getStatusLabel(equipmentInfo.status) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>能耗等级：</label>
                <el-tag :color="getEnergyLevelColor(equipmentInfo.energyLevel)" class="text-white border-none">
                  {{ getEnergyLevelLabel(equipmentInfo.energyLevel) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>额定功率：</label>
                <span>{{ equipmentInfo.ratedPower || '-' }} kW</span>
              </div>
              <div class="info-item">
                <label>制造商：</label>
                <span>{{ equipmentInfo.manufacturer || '-' }}</span>
              </div>
              <div class="info-item">
                <label>型号规格：</label>
                <span>{{ equipmentInfo.model || '-' }}</span>
              </div>
              <div class="info-item">
                <label>购买日期：</label>
                <span>{{ formatDate(equipmentInfo.purchaseDate) || '-' }}</span>
              </div>
              <div class="info-item">
                <label>安装日期：</label>
                <span>{{ formatDate(equipmentInfo.installDate) || '-' }}</span>
              </div>
              <div class="info-item">
                <label>保修期至：</label>
                <span>{{ formatDate(equipmentInfo.warrantyEndDate) || '-' }}</span>
              </div>
              <div class="info-item full-width">
                <label>设备描述：</label>
                <span>{{ equipmentInfo.description || '-' }}</span>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 能耗信息 -->
        <el-tab-pane label="能耗信息" name="energy">
          <div class="info-section">
            <div class="section-title">
              <IconifyIconOffline icon="RI-FlashLine" />
              <span>能耗统计</span>
            </div>
            <div class="energy-stats">
              <div class="stat-card">
                <div class="stat-value">{{ energyData.todayConsumption || 0 }}</div>
                <div class="stat-label">今日能耗 (kWh)</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ energyData.monthConsumption || 0 }}</div>
                <div class="stat-label">本月能耗 (kWh)</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ energyData.yearConsumption || 0 }}</div>
                <div class="stat-label">本年能耗 (kWh)</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ energyData.avgConsumption || 0 }}</div>
                <div class="stat-label">平均日耗 (kWh)</div>
              </div>
            </div>

            <!-- 能耗趋势图表区域 -->
            <div class="chart-container">
              <div class="chart-title">能耗趋势</div>
              <div class="chart-placeholder">
                <IconifyIconOffline icon="RI-BarChartLine" style="font-size: 48px; color: #ddd;" />
                <p>能耗趋势图表开发中...</p>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 维护记录 -->
        <el-tab-pane label="维护记录" name="maintenance">
          <div class="info-section">
            <div class="section-title">
              <IconifyIconOffline icon="RI-ToolsLine" />
              <span>维护记录</span>
            </div>
            <div class="maintenance-list">
              <el-timeline>
                <el-timeline-item
                  v-for="(record, index) in maintenanceRecords"
                  :key="index"
                  :timestamp="formatDate(record.maintenanceDate)"
                  placement="top"
                >
                  <div class="maintenance-item">
                    <div class="maintenance-header">
                      <span class="maintenance-type">{{ record.maintenanceType }}</span>
                      <el-tag
                        :type="record.status === 'completed' ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ record.status === 'completed' ? '已完成' : '进行中' }}
                      </el-tag>
                    </div>
                    <div class="maintenance-content">
                      <p><strong>维护内容：</strong>{{ record.content }}</p>
                      <p><strong>维护人员：</strong>{{ record.maintainer }}</p>
                      <p><strong>费用：</strong>{{ record.cost || 0 }} 元</p>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>

              <div v-if="maintenanceRecords.length === 0" class="empty-state">
                <IconifyIconOffline icon="RI-FileList3Line" style="font-size: 48px; color: #ddd;" />
                <p>暂无维护记录</p>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 操作日志 -->
        <el-tab-pane label="操作日志" name="logs">
          <div class="info-section">
            <div class="section-title">
              <IconifyIconOffline icon="RI-HistoryLine" />
              <span>操作日志</span>
            </div>
            <div class="logs-table">
              <el-table :data="operationLogs" style="width: 100%">
                <el-table-column prop="operationTime" label="操作时间" width="180">
                  <template #default="{ row }">
                    {{ formatDateTime(row.operationTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="operationType" label="操作类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.operationType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="operator" label="操作人员" width="120" />
                <el-table-column prop="description" label="操作描述" />
                <el-table-column prop="result" label="操作结果" width="100">
                  <template #default="{ row }">
                    <el-tag
                      :type="row.result === 'success' ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ row.result === 'success' ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>

              <div v-if="operationLogs.length === 0" class="empty-state">
                <IconifyIconOffline icon="RI-FileList3Line" style="font-size: 48px; color: #ddd;" />
                <p>暂无操作日志</p>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, onMounted, computed, getCurrentInstance } from "vue";
import dayjs from "dayjs";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  queryEnergyEquipmentDetail,
  queryEquipmentEnergyConsumption,
  queryEquipmentMaintenanceRecords,
  deleteEnergyEquipment
} from "../api/index";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

// 接收父组件传递的数据
const props = defineProps({
  needsPassedData: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(["back", "jump-to"]);

// 响应式数据
const activeTab = ref("basic");

interface EquipmentInfo {
  equipmentId?: string;
  equipmentName?: string;
  equipmentCode?: string;
  equipmentType?: string;
  categoryName?: string;
  deptName?: string;
  location?: string;
  status?: string;
  energyLevel?: string;
  ratedPower?: number;
  manufacturer?: string;
  model?: string;
  purchaseDate?: string;
  installDate?: string;
  warrantyEndDate?: string;
  description?: string;
  [key: string]: any;
}

interface EnergyData {
  todayConsumption?: number;
  monthConsumption?: number;
  yearConsumption?: number;
  avgConsumption?: number;
}

interface MaintenanceRecord {
  maintenanceDate: string;
  maintenanceType: string;
  content: string;
  maintainer: string;
  cost?: number;
  status: string;
}

interface OperationLog {
  operationTime: string;
  operationType: string;
  operator: string;
  description: string;
  result: string;
}

const state = reactive({
  loading: false,
  equipmentInfo: {} as EquipmentInfo,
  energyData: {} as EnergyData,
  maintenanceRecords: [] as MaintenanceRecord[],
  operationLogs: [] as OperationLog[]
});

const { loading, equipmentInfo, energyData, maintenanceRecords, operationLogs } = toRefs(state);

// 获取设备ID
const equipmentId = computed(() => {
  return props.needsPassedData?.rowData?.equipmentId || '';
});

// 工具方法 - 获取设备状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    'running': 'success',
    'stopped': 'info',
    'maintenance': 'warning',
    'fault': 'danger'
  };
  return statusMap[status] || 'info';
};

// 工具方法 - 获取设备状态标签
const getStatusLabel = (status: string) => {
  const statusMap = {
    'running': '运行中',
    'stopped': '停机',
    'maintenance': '维护中',
    'fault': '故障'
  };
  return statusMap[status] || '未知';
};

// 工具方法 - 获取能耗等级颜色
const getEnergyLevelColor = (level: string) => {
  const colorMap = {
    'A': '#67C23A', // 绿色 - 优秀
    'B': '#E6A23C', // 橙色 - 良好
    'C': '#F56C6C', // 红色 - 一般
    'D': '#909399'  // 灰色 - 较差
  };
  return colorMap[level] || '#909399';
};

// 工具方法 - 获取能耗等级标签
const getEnergyLevelLabel = (level: string) => {
  const labelMap = {
    'A': 'A级(优秀)',
    'B': 'B级(良好)',
    'C': 'C级(一般)',
    'D': 'D级(较差)'
  };
  return labelMap[level] || '未评级';
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD');
};

// 格式化日期时间
const formatDateTime = (date: string) => {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 加载设备详情数据
const loadEquipmentDetail = async () => {
  if (!equipmentId.value) {
    $message.error("设备ID不能为空");
    return;
  }

  state.loading = true;
  try {
    // 加载基本信息
    const detailRes = await queryEnergyEquipmentDetail(equipmentId.value);
    state.equipmentInfo = detailRes.data || {};

    // 加载能耗数据
    const energyRes = await queryEquipmentEnergyConsumption({ equipmentId: equipmentId.value });
    state.energyData = energyRes.data || {};

    // 加载维护记录
    const maintenanceRes = await queryEquipmentMaintenanceRecords(equipmentId.value);
    state.maintenanceRecords = maintenanceRes.data || [];

    // 模拟操作日志数据（实际项目中应该从接口获取）
    state.operationLogs = [
      {
        operationTime: '2024-01-15 10:30:00',
        operationType: '状态变更',
        operator: '张三',
        description: '设备状态从停机变更为运行中',
        result: 'success'
      },
      {
        operationTime: '2024-01-10 14:20:00',
        operationType: '参数调整',
        operator: '李四',
        description: '调整设备运行参数',
        result: 'success'
      },
      {
        operationTime: '2024-01-05 09:15:00',
        operationType: '维护保养',
        operator: '王五',
        description: '定期维护保养',
        result: 'success'
      }
    ];

  } catch (error) {
    console.error("加载设备详情失败:", error);
    $message.error("加载设备详情失败");
  } finally {
    state.loading = false;
  }
};

// 返回列表
const handleBack = () => {
  emit("back");
};

// 编辑设备
const handleEdit = () => {
  $message.info("编辑设备功能开发中...");
};

// 删除设备
const handleDelete = () => {
  $confirm(`您确认要删除设备"${state.equipmentInfo.equipmentName}"吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteEnergyEquipment([equipmentId.value]);
      $message.success("删除成功");
      handleBack(); // 删除成功后返回列表
    } catch (error) {
      console.error("删除设备失败:", error);
      $message.error("删除设备失败");
    }
  });
};

// 组件挂载时加载数据
onMounted(() => {
  // 如果有传递的行数据，先使用传递的数据
  if (props.needsPassedData?.rowData) {
    state.equipmentInfo = { ...props.needsPassedData.rowData };
  }

  // 然后加载完整的详情数据
  loadEquipmentDetail();
});
</script>

<style scoped lang="scss">
.energy-equipment-details {
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;

      .equipment-title {
        display: flex;
        align-items: center;
        gap: 12px;

        h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #303133;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .detail-content {
    padding: 0 24px;

    .detail-tabs {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      :deep(.el-tabs__header) {
        margin-bottom: 20px;

        .el-tabs__nav-wrap {
          &::after {
            background-color: #e4e7ed;
          }
        }

        .el-tabs__item {
          font-size: 16px;
          font-weight: 500;

          &.is-active {
            color: #409EFF;
          }
        }
      }
    }
  }

  .info-section {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      .iconify {
        color: #409EFF;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px 24px;

      .info-item {
        display: flex;
        align-items: center;

        &.full-width {
          grid-column: 1 / -1;
          align-items: flex-start;

          label {
            margin-top: 2px;
          }

          span {
            line-height: 1.6;
          }
        }

        label {
          min-width: 100px;
          font-weight: 500;
          color: #606266;
          margin-right: 12px;
        }

        span {
          color: #303133;
          flex: 1;
        }
      }
    }
  }

  .energy-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;

    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px;
      border-radius: 12px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

      .stat-value {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        opacity: 0.9;
      }

      &:nth-child(2) {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
      }

      &:nth-child(3) {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
      }

      &:nth-child(4) {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        box-shadow: 0 4px 12px rgba(67, 233, 123, 0.3);
      }
    }
  }

  .chart-container {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 20px;

    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 20px;
    }

    .chart-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      color: #909399;

      p {
        margin-top: 12px;
        font-size: 14px;
      }
    }
  }

  .maintenance-list {
    .maintenance-item {
      .maintenance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .maintenance-type {
          font-weight: 600;
          color: #303133;
        }
      }

      .maintenance-content {
        color: #606266;
        line-height: 1.6;

        p {
          margin: 4px 0;
        }
      }
    }
  }

  .logs-table {
    :deep(.el-table) {
      border-radius: 8px;
      overflow: hidden;

      .el-table__header {
        background-color: #fafafa;
      }

      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;

    p {
      margin-top: 12px;
      font-size: 14px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .detail-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-left {
        width: 100%;

        .equipment-title {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }

      .header-right {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .energy-stats {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 480px) {
    .energy-stats {
      grid-template-columns: 1fr;
    }

    .detail-content {
      padding: 0 12px;
    }

    .detail-header {
      padding: 16px 12px;
    }
  }
}
</style>
