<template>
  <div class="energy-equipment-home">
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <!-- 使用el-tabs组件，设置默认激活的标签页 -->
        <el-tabs v-model="activeTabName" class="ml-2 mr-2" @tab-change="viewTabChangeHandler">
          <!-- 设备分类视图标签页 -->
          <el-tab-pane name="categoryView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-Settings3Fill" />
                <span class="ml-1">设备分类</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索设备分类 -->
              <div class="pb-1.5 flex items-center">
                <el-input
                  placeholder="设备分类名称"
                  :suffix-icon="useRenderIcon('EP-Search')"
                  clearable
                  v-model="categoryKeyWord"
                />
                <el-icon @click="loadCategoryData" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示设备分类结构 -->
              <el-tree
                ref="categoryTreeRef"
                :data="categoryData"
                node-key="id"
                :expand-on-click-node="false"
                :props="{
                  label: 'categoryName',
                  children: 'children'
                }"
                highlight-current
                default-expand-all
                :filter-node-method="filterCategoryNode"
                :style="treeStyle"
                @current-change="categoryChangeHandler"
              >
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.equipmentCount || 0) > 0 ? '#409EFF' : 'unset'
                  }">{{ node.label }}
                    <span v-if="data.equipmentCount > 0" style="font-size: 12px; color: #909399;">
                      ({{ data.equipmentCount }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>

          <!-- 组织视图标签页 -->
          <el-tab-pane name="deptView">
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-OrganizationChart" />
                <span class="ml-1">组织视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索组织 -->
              <div class="pb-1.5 flex items-center">
                <el-input
                  placeholder="组织名称"
                  :suffix-icon="useRenderIcon('EP-Search')"
                  clearable
                  v-model="deptKeyWord"
                />
                <el-icon @click="loadDeptData" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 组织树形控件 -->
              <el-tree
                ref="deptTreeRef"
                :data="deptData"
                node-key="deptId"
                :expand-on-click-node="false"
                :props="{
                  label: 'deptName',
                  children: 'children'
                }"
                highlight-current
                default-expand-all
                :filter-node-method="filterDeptNode"
                :style="treeStyle"
                @current-change="deptChangeHandler"
              >
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.equipmentCount || 0) > 0 ? '#409EFF' : 'unset'
                  }">{{ node.label }}
                    <span v-if="data.equipmentCount > 0" style="font-size: 12px; color: #909399;">
                      ({{ data.equipmentCount }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>

      <!-- 右侧面板 -->
      <template #paneR>
        <!-- 搜索区域 -->
        <div class="flex justify-center items-center mb-6 mr-6">
          <search-with-column
            v-model="columnCondition.value"
            v-model:fuzzy-enable="columnCondition.fuzzyable"
            v-model:column-val="columnCondition.field"
            :column-options="columnSearchOptions"
            :column-select-width="90"
            @search="resetTablePageAndQuery('', 'toggleQueryCriteria')"
            @reset="resetSearchHandler"
            class="flex-c w-4/5"
            input-class-name="w-3/5"
          />
        </div>

        <!-- 设备状态筛选 -->
        <div class="flex mt-6 mb-2">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">设备状态:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag
                class="mr-2 tag-container"
                :checked="state.statusCheckAll"
                @change="statusChangeHandler(null)"
              >
                全部
              </el-check-tag>
              <el-check-tag
                style="margin-bottom: 6px"
                v-for="item in state.statusData"
                :key="item.value"
                :checked="!state.statusCheckAll && item.checked"
                class="mr-2"
                @change="statusChangeHandler(item)"
              >
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 设备类型筛选 -->
        <div class="flex mt-2 mb-3">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">设备类型:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag
                class="mr-2 tag-container"
                :checked="state.typeCheckAll"
                @change="typeChangeHandler(null)"
              >
                全部
              </el-check-tag>
              <el-check-tag
                style="margin-bottom: 6px"
                v-for="item in state.typeData"
                :key="item.value"
                :checked="!state.typeCheckAll && item.checked"
                class="mr-2"
                @change="typeChangeHandler(item)"
              >
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 分割线 -->
        <div class="dashed-divider mx-4 my-1 pr-6"></div>

        <!-- 表格 -->
        <div class="ml-2 mr-2">
          <im-table
            ref="tableRef"
            :data="tableData"
            :columns="tableColumns"
            center
            toolbar
            :table-alert="{
              closable: false
            }"
            operator
            :height="tableOption.height"
            :stripe="tableOption.stripe"
            show-checkbox
            :column-storage="createColumnStorage('energy-equipment-management', 'remote')"
            :pagination="tablePage"
            :loading="tableLoading"
            :filter-data-provider="filterDataProvider"
            @on-reload="resetTablePageAndQuery"
            @selection-change="selectionChangeHandler"
            @on-page-change="queryEquipmentData"
          >
            <!-- 自定义 alert 提示内容 -->
            <template #tip>
              <span class="statistics-tip" style="margin-left: 100px">
                <span class="statistics-item mr-2 ml-2" v-for="(item, index) in equipmentStatisticsData"
                  :key="index">
                  <span class="label">{{ item.label }}: </span>
                  <span class="count text-[16px] font-bold ml-1">{{ item.value || 0 }}</span>
                </span>
              </span>
            </template>

            <!-- 表格右侧菜单 -->
            <template #toolbar-right="{ size }">
              <div class="float-left flex-sc pr-3 gap-3">
                <!-- 日期选择器 -->
                <el-date-picker
                  clearable
                  v-model="dateTimeRange"
                  type="daterange"
                  range-separator="到"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 200px"
                  @change="dateChangeFunction"
                />

                <!-- 批量操作 -->
                <el-dropdown style="margin: 0 10px">
                  <el-button type="primary"> 批量操作 </el-button>
                  <template #dropdown>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        :disabled="state.selectedEquipmentRows.length == 0"
                        style="padding: 0.4rem 1rem"
                        @click.native="handleBatchOperation('export')"
                      >
                        批量导出
                      </el-dropdown-item>
                      <el-dropdown-item
                        style="padding: 0.4rem 1rem"
                        :disabled="state.selectedEquipmentRows.length == 0"
                        @click="handleBatchOperation('delete')"
                        size="small"
                        type="primary"
                      >
                        批量删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <!-- 导出数据 -->
                <el-tooltip content="导出数据" placement="top" :open-delay="1000">
                  <el-button
                    style="margin-left: 0.1rem"
                    :icon="useRenderIcon('EP-Download')"
                    circle
                    :size="size"
                    :disabled="tableData.length == 0"
                    @click="exportEquipmentHandler"
                  />
                </el-tooltip>

                <!-- 新增设备 -->
                <el-tooltip content="新增设备" placement="top" :open-delay="1000">
                  <el-button
                    style="margin-left: 0.1rem"
                    :icon="useRenderIcon('EP-Plus')"
                    circle
                    :size="size"
                    type="primary"
                    @click="addEquipmentHandler"
                  />
                </el-tooltip>
              </div>
            </template>

            <!-- 表格操作按钮 -->
            <template #operator="{ row, size }">
              <!-- 详情按钮 -->
              <el-button
                :size="size"
                type="primary"
                text
                :icon="useRenderIcon('EP-View')"
                @click="detailViewHandler(row)"
              >
                详情
              </el-button>
              <!-- 编辑按钮 -->
              <el-button
                :size="size"
                type="warning"
                text
                :icon="useRenderIcon('EP-Edit')"
                @click="editEquipmentHandler(row)"
              >
                编辑
              </el-button>
              <!-- 删除按钮 -->
              <el-button
                :size="size"
                type="danger"
                text
                :icon="useRenderIcon('EP-Delete')"
                @click="deleteEquipmentHandler(row)"
              >
                删除
              </el-button>
            </template>

            <!-- 设备状态列 -->
            <template #status="{ row }">
              <el-tag :type="getStatusType(row.status)" effect="light">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>

            <!-- 设备类型列 -->
            <template #equipmentType="{ row }">
              <el-tag>{{ row.equipmentType }}</el-tag>
            </template>

            <!-- 能耗等级列 -->
            <template #energyLevel="{ row }">
              <el-tag :color="getEnergyLevelColor(row.energyLevel)" class="text-white border-none">
                {{ getEnergyLevelLabel(row.energyLevel) }}
              </el-tag>
            </template>
          </im-table>
        </div>
      </template>
    </splitpane>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch,
  h,
  CSSProperties,
  markRaw
} from "vue";
import dayjs from "dayjs";
import { Refresh, Download } from "@element-plus/icons-vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";

// 导入API接口
import {
  queryEnergyEquipmentList,
  queryEquipmentCategoryTree,
  getUserDeptTreeAxios,
  exportEnergyEquipmentData,
  deleteEnergyEquipment,
  queryEquipmentStatistics
} from "../api/index";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const categoryTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const deptTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const tableRef = ref<ImTableInstance>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "categoryName",
  children: "children",
  isLeaf: "leaf"
};

// 设备分类数据
const categoryData = ref<any[]>([]);
// 组织数据
const deptData = ref<any[]>([]);

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

// 动态高度
const tmpWidth = ref(0);
const filterTagRef = ref<HTMLElement | null>(null);
const tableKey = ref(0);

// 计算表格高度的函数
const calculateTableHeight = () => {
  nextTick(() => {
    nextTick(() => {
      // 获取筛选区域的高度
      if (!filterTagRef.value) return;
      const filterHeight = filterTagRef.value.offsetHeight;

      // 计算总高度
      tmpWidth.value = filterHeight + 40; // 额外的间距

      // 计算表格高度
      tableHeight.value =
        document.documentElement.offsetHeight - 350 - tmpWidth.value;
      tableOption.height = tableHeight.value;

      tableKey.value = tableKey.value + 1;
    });
  });
};

//根据页面高度设置表格高度
const tableHeight = ref(0);

interface TableColumnType {
  hide: boolean;
  prop: string;
  label: string;
  fieldType: string;
  width: string | number;
  unit?: string;
  component?: any;
  filters?: boolean;
  sortable?: boolean;
  [key: string]: any;
}

interface TableOptionType {
  align: string;
  menuAlign: string;
  border: boolean;
  stripe: boolean;
  height?: number;
  rowKey: string;
  column: TableColumnType[];
  [key: string]: any;
}

const tableOption = reactive<TableOptionType>({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 180,
  height: tableHeight.value,
  rowKey: "equipmentId",
  column: []
});

// 表格列定义
const tableColumns = computed(() => {
  return [
    {
      label: "设备名称",
      prop: "equipmentName",
      width: "200px",
      align: "left",
      filterable: true,
      showOverflowTooltip: true,
      meta: {
        filterType: "input"
      }
    },
    {
      label: "设备编号",
      prop: "equipmentCode",
      width: "150px",
      align: "center",
      filterable: true,
      meta: {
        filterType: "input"
      }
    },
    {
      label: "设备类型",
      prop: "equipmentType",
      width: "120px",
      align: "center",
      slot: "equipmentType",
      filterable: true,
      meta: {
        filterType: "select",
        options: state.typeData.map(item => ({
          label: item.label,
          value: item.value
        }))
      }
    },
    {
      label: "设备状态",
      prop: "status",
      width: "100px",
      align: "center",
      slot: "status",
      filterable: true,
      meta: {
        filterType: "select",
        options: state.statusData.map(item => ({
          label: item.label,
          value: item.value
        }))
      }
    },
    {
      label: "能耗等级",
      prop: "energyLevel",
      width: "120px",
      align: "center",
      slot: "energyLevel",
      filterable: true,
      meta: {
        filterType: "select",
        options: [
          { label: "A级(优秀)", value: "A" },
          { label: "B级(良好)", value: "B" },
          { label: "C级(一般)", value: "C" },
          { label: "D级(较差)", value: "D" }
        ]
      }
    },
    {
      label: "额定功率(kW)",
      prop: "ratedPower",
      width: "120px",
      align: "center",
      filterable: true,
      sortable: true,
      meta: {
        filterType: "input"
      }
    },
    {
      label: "所属组织",
      prop: "deptName",
      width: "150px",
      align: "left",
      showOverflowTooltip: true,
      filterable: true,
      meta: {
        filterType: "input"
      }
    },
    {
      label: "安装位置",
      prop: "location",
      width: "150px",
      align: "left",
      showOverflowTooltip: true,
      filterable: true,
      meta: {
        filterType: "input"
      }
    },
    {
      label: "制造商",
      prop: "manufacturer",
      width: "120px",
      align: "center",
      filterable: true,
      meta: {
        filterType: "input"
      }
    },
    {
      label: "型号规格",
      prop: "model",
      width: "120px",
      align: "center",
      showOverflowTooltip: true,
      filterable: true,
      meta: {
        filterType: "input"
      }
    },
    {
      label: "安装日期",
      prop: "installDate",
      width: "120px",
      align: "center",
      filterable: true,
      sortable: true,
      meta: {
        filterType: "date"
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: "150px",
      align: "center",
      filterable: true,
      sortable: true,
      meta: {
        filterType: "datetime"
      }
    }
  ];
});

//数据对象
interface StateType {
  activeTabName: string;
  tableLoading: boolean;
  categoryKeyWord: string | null;
  deptKeyWord: string | null;
  columnCondition: {
    value: string | null;
    field: string;
    fuzzyable: boolean;
    operator: string;
  };
  dateTimeRange: any[];
  searchCondition: {
    categoryId: string;
    deptId: string;
    status: string;
    equipmentType: string;
    [key: string]: any;
  };
  statusCheckAll: boolean;
  typeCheckAll: boolean;
  statusData: Array<{ label: string; value: string; checked: boolean }>;
  typeData: Array<{ label: string; value: string; checked: boolean }>;
  columnSearchOptions: any[];
  tableData: any[];
  tablePage: {
    align: string;
    total: number;
    currentPage: number;
    pageSize: number;
    pageSizes: number[];
  };
  selectedEquipments: string[];
  selectedEquipmentRows: any[];
  equipmentStatisticsData: Array<{ label: string; code: string; value: number }>;
  filters: any[];
}

const state = reactive<StateType>({
  activeTabName: "categoryView", // 默认激活设备分类视图
  tableLoading: false,
  categoryKeyWord: null,
  deptKeyWord: null,
  columnCondition: {
    value: null,
    field: "equipmentName", // 使用设备名称作为默认搜索字段
    fuzzyable: true,
    operator: "fuzzy"
  },
  dateTimeRange: [],
  searchCondition: {
    categoryId: "",
    deptId: "",
    status: "",
    equipmentType: ""
  },

  statusCheckAll: true,
  typeCheckAll: true,

  statusData: [
    {
      label: "运行中",
      value: "running",
      checked: false
    },
    {
      label: "停机",
      value: "stopped",
      checked: false
    },
    {
      label: "维护中",
      value: "maintenance",
      checked: false
    },
    {
      label: "故障",
      value: "fault",
      checked: false
    }
  ],

  typeData: [
    {
      label: "空调设备",
      value: "air_conditioning",
      checked: false
    },
    {
      label: "照明设备",
      value: "lighting",
      checked: false
    },
    {
      label: "供暖设备",
      value: "heating",
      checked: false
    },
    {
      label: "通风设备",
      value: "ventilation",
      checked: false
    },
    {
      label: "其他设备",
      value: "other",
      checked: false
    }
  ],

  columnSearchOptions: [
    { label: "设备名称", value: "equipmentName" },
    { label: "设备编号", value: "equipmentCode" },
    { label: "设备类型", value: "equipmentType" },
    { label: "制造商", value: "manufacturer" },
    { label: "型号规格", value: "model" },
    { label: "安装位置", value: "location" },
    { label: "所属组织", value: "deptName" }
  ],
  tableData: [],
  tablePage: {
    align: "right",
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  selectedEquipments: [] as Array<string>,
  selectedEquipmentRows: [],

  equipmentStatisticsData: [
    {
      label: "设备总数",
      code: "total",
      value: 0
    },
    {
      label: "运行中",
      code: "running",
      value: 0
    },
    {
      label: "停机",
      code: "stopped",
      value: 0
    },
    {
      label: "维护中",
      code: "maintenance",
      value: 0
    },
    {
      label: "故障",
      code: "fault",
      value: 0
    }
  ],

  filters: []
});

const {
  activeTabName,
  tableLoading,
  categoryKeyWord,
  deptKeyWord,
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  equipmentStatisticsData
} = toRefs(state);

// 监听搜索关键字变化
watch(categoryKeyWord, val => {
  categoryTreeRef.value!.filter(val);
});

watch(deptKeyWord, val => {
  deptTreeRef.value!.filter(val);
});

const treeStyle = computed((): CSSProperties => {
  return {
    height: tableHeight.value + 80 + "px",
    overflow: "auto"
  };
});

//设备分类数据搜索
const filterCategoryNode = (value: string, data: any) => {
  if (!value) return true;

  // 根节点直接返回true
  if (data.categoryName == "全部分类" || data.id == "all") return true;

  return data.categoryName && data.categoryName.indexOf(value) > -1;
};

//组织数据搜索
const filterDeptNode = (value: string, data: SimpleDeptInfo) => {
  if (!value) return true;

  // 根节点直接返回true
  if (!data.deptId) {
    return true;
  }

  return data.deptName.indexOf(value) > -1;
};

//设备分类树选择改变触发
const categoryChangeHandler = (data: any) => {
  if (data && data.id) {
    state.searchCondition.categoryId = data.id;
  } else {
    state.searchCondition.categoryId = "";
  }
  resetTablePageAndQuery();
};

//组织树选择改变触发
const deptChangeHandler = (data: any) => {
  if (data && data.deptId) {
    state.searchCondition.deptId = data.deptId;
  } else {
    state.searchCondition.deptId = "";
  }
  resetTablePageAndQuery();
};

//视图标签改变触发
const viewTabChangeHandler = (activeName: string) => {
  if (activeName === "categoryView") {
    //处理设备分类视图初始化
    state.searchCondition.deptId = "";
    state.searchCondition.categoryId = "";
    loadCategoryData();
  } else if (activeName === "deptView") {
    //处理组织视图初始化
    state.searchCondition.categoryId = "";
    state.searchCondition.deptId = "";
    loadDeptData();
  }
  resetTablePageAndQuery();
};

//加载设备分类数据
const loadCategoryData = async () => {
  try {
    const res = await queryEquipmentCategoryTree();
    const categoryOptions = res.data || [];

    state.categoryData = [
      {
        id: "all",
        categoryName: "全部分类",
        children: categoryOptions
      }
    ];

    // 设置默认选中
    nextTick(() => {
      if (categoryTreeRef.value) {
        categoryTreeRef.value.setCurrentKey("all");
      }
    });
  } catch (error) {
    console.error("加载设备分类数据失败:", error);
    $message.error("加载设备分类数据失败");
  }
};

//加载组织数据
const loadDeptData = async () => {
  try {
    const res = await getUserDeptTreeAxios();
    const deptOptions = res.data || [];

    state.deptData = [
      {
        deptId: "all",
        deptName: "全部组织",
        children: deptOptions
      }
    ];

    // 设置默认选中
    nextTick(() => {
      if (deptTreeRef.value) {
        deptTreeRef.value.setCurrentKey("all");
      }
    });
  } catch (error) {
    console.error("加载组织数据失败:", error);
    $message.error("加载组织数据失败");
  }
};

//设备状态标签选中改变
const statusChangeHandler = (tag: any) => {
  state.searchCondition.status = tag ? tag.value : "";
  state.statusCheckAll = !tag;
  state.statusData.forEach((item: any) => {
    item.checked = item.value === tag?.value;
  });
  resetTablePageAndQuery();
};

//设备类型标签选中改变
const typeChangeHandler = (tag: any) => {
  state.searchCondition.equipmentType = tag ? tag.value : "";
  state.typeCheckAll = !tag;
  state.typeData.forEach((item: any) => {
    item.checked = item.value === tag?.value;
  });
  resetTablePageAndQuery();
};

//重置分页后查询设备数据
const resetTablePageAndQuery = (
  info = "",
  toggleQueryCriteria = "",
  keepFiltersFlag?: boolean
) => {
  if (keepFiltersFlag !== true) {
    // 只要没有声明keepFilters = true，清除表格所有过滤器
    state.filters = [];
    tableRef.value.clearAllFilters();
  }
  state.tablePage.currentPage = 1;
  queryEquipmentData();
};

/**
 * 日期范围计算
 */
const computedDateRange = computed(() => {
  let dateRange: string | string[] | null;
  if (state?.dateTimeRange?.length == 2) {
    dateRange = [
      dayjs(state?.dateTimeRange?.[0]).format("YYYY-MM-DD HH:mm:ss"),
      dayjs(state?.dateTimeRange?.[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
    ];
  } else {
    dateRange = null;
  }
  return dateRange;
});

const buildQueryCondition = () => {
  const tmpConditions = [];

  // 如果有列搜索条件，将其添加到 conditions 数组中
  if (state?.columnCondition.value) {
    tmpConditions.push(state.columnCondition);
  }

  return {
    //查询条件
    conditions: tmpConditions,
    //日期范围
    dateRange: computedDateRange.value,
    //搜索条件
    categoryId: state?.searchCondition?.categoryId,
    deptId: state?.searchCondition?.deptId,
    status: state?.searchCondition?.status,
    equipmentType: state?.searchCondition?.equipmentType,
    //当前页码
    pageNum: state?.tablePage?.currentPage,
    //每页显示条数
    pageSize: state?.tablePage?.pageSize,
    // 列头过滤器
    headerFilter: {
      filters: state?.filters
    }
  };
};

//查询设备数据
const queryEquipmentData = async () => {
  state.selectedEquipmentRows = [];
  //设置表格加载状态为true
  state.tableLoading = true;
  state.tableData = [];
  dealFuzzEnable();

  try {
    // 查询条件
    const queryParams = buildQueryCondition();
    console.log("queryParams=========================>", queryParams);

    // 查询统计数据(异步)
    queryEquipmentStatisticsData(queryParams);

    //根据条件查询设备列表
    const res = await queryEnergyEquipmentList(queryParams);
    //设置表格数据
    state.tableData = res["data"]?.list || [];

    //设置表格总条数
    state.tablePage.total = res["data"]?.total || 0;

    // 调用计算表格高度
    calculateTableHeight();
  } catch (e) {
    console.error(e);
    $message.error("查询设备数据失败");
  }
  //设置表格加载状态为false
  state.tableLoading = false;
};

// 查询设备统计数据
const queryEquipmentStatisticsData = (queryParams: any) => {
  queryEquipmentStatistics(queryParams).then((res: any) => {
    let resData = res.data || {};
    for (let item of state.equipmentStatisticsData) {
      let { code } = item;
      item.value = resData[code] || 0;
    }
  }).catch(error => {
    console.error("查询统计数据失败:", error);
  });
};

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzyable) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "equipmentName",
    fuzzyable: true,
    operator: "fuzzy"
  };
  //重置时间范围
  state.dateTimeRange = [];
  //重置设备状态
  state.searchCondition.status = "";
  state.statusCheckAll = true;
  state.statusData.forEach((item: any) => (item.checked = false));
  //重置设备类型
  state.searchCondition.equipmentType = "";
  state.typeCheckAll = true;
  state.typeData.forEach((item: any) => (item.checked = false));

  resetTablePageAndQuery("设备重置", "toggleQueryCriteria");
};

const dateChangeFunction = (query: any) => {
  resetTablePageAndQuery();
};

//查看设备详情触发
const detailViewHandler = (evt: any) => {
  emit("event-select", evt);
  let needsPassedData = {};
  needsPassedData["rowData"] = evt;

  // 跳转到详情页面
  jumpTo("energyEquipmentDetail", needsPassedData);
};

// 跳转方法
const jumpTo = (componentName: string, data: any) => {
  emit("jump-to", componentName, data);
};

//新增设备处理
const addEquipmentHandler = () => {
  // 这里可以打开新增设备的对话框
  $message.info("新增设备功能开发中...");
};

//编辑设备处理
const editEquipmentHandler = (row: any) => {
  // 这里可以打开编辑设备的对话框
  $message.info("编辑设备功能开发中...");
};

//删除设备处理
const deleteEquipmentHandler = (row: any) => {
  $confirm(`您确认要删除设备"${row.equipmentName}"吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteEnergyEquipment([row.equipmentId]);
      $message.success("删除成功");
      queryEquipmentData();
    } catch (error) {
      console.error("删除设备失败:", error);
      $message.error("删除设备失败");
    }
  });
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds: string[] = [];
  selRows.forEach(row => selectIds.push(row.equipmentId));
  state.selectedEquipments = selectIds;
  state.selectedEquipmentRows = selRows;
};

//批量操作处理
const handleBatchOperation = (operation: string) => {
  if (operation === 'export') {
    exportEquipmentHandler();
  } else if (operation === 'delete') {
    batchDeleteHandler();
  }
};

//批量删除处理
const batchDeleteHandler = () => {
  if (state.selectedEquipments.length === 0) {
    $message.warning("请先选择要删除的设备");
    return;
  }

  $confirm(`您确认要删除选中的${state.selectedEquipments.length}个设备吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteEnergyEquipment(state.selectedEquipments);
      $message.success("批量删除成功");
      queryEquipmentData();
    } catch (error) {
      console.error("批量删除设备失败:", error);
      $message.error("批量删除设备失败");
    }
  });
};

//导出设备数据触发
const exportEquipmentHandler = () => {
  $confirm(`您确认要导出当前查询条件下的数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    try {
      $message({
        message: "数据正在导出中...",
        type: "success"
      });
      const queryParams = buildQueryCondition();
      await exportEnergyEquipmentData(queryParams);
      $message.success("导出成功");
    } catch (error) {
      console.error("导出数据失败:", error);
      $message.error("导出数据失败");
    }
  });
};

// 工具方法 - 获取设备状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    'running': 'success',
    'stopped': 'info',
    'maintenance': 'warning',
    'fault': 'danger'
  };
  return statusMap[status] || 'info';
};

// 工具方法 - 获取设备状态标签
const getStatusLabel = (status: string) => {
  const statusMap = {
    'running': '运行中',
    'stopped': '停机',
    'maintenance': '维护中',
    'fault': '故障'
  };
  return statusMap[status] || '未知';
};

// 工具方法 - 获取能耗等级颜色
const getEnergyLevelColor = (level: string) => {
  const colorMap = {
    'A': '#67C23A', // 绿色 - 优秀
    'B': '#E6A23C', // 橙色 - 良好
    'C': '#F56C6C', // 红色 - 一般
    'D': '#909399'  // 灰色 - 较差
  };
  return colorMap[level] || '#909399';
};

// 工具方法 - 获取能耗等级标签
const getEnergyLevelLabel = (level: string) => {
  const labelMap = {
    'A': 'A级(优秀)',
    'B': 'B级(良好)',
    'C': 'C级(一般)',
    'D': 'D级(较差)'
  };
  return labelMap[level] || '未评级';
};

// 过滤数据提供者
const filterDataProvider: TableFilterDataProvider = async (column, keyword) => {
  // 这里可以根据需要实现动态过滤数据
  return [];
};

// 窗口大小变化时重新计算表格高度
const handleResize = () => {
  calculateTableHeight();
};

// 组件挂载时的初始化
onMounted(() => {
  // 默认加载设备分类数据
  loadCategoryData();
  // 初始化查询设备数据
  queryEquipmentData();
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped lang="scss">
.energy-equipment-home {
  width: 100%;
  height: 100%;

  .flex-c {
    display: flex;
    align-items: center;
  }

  .flex-sc {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .tag-container {
    margin-bottom: 6px;
  }

  .dashed-divider {
    border-top: 1px dashed #e4e7ed;
  }

  .statistics-tip {
    display: flex;
    align-items: center;

    .statistics-item {
      display: flex;
      align-items: center;

      .label {
        color: #606266;
        font-size: 14px;
      }

      .count {
        color: #409EFF;
        font-weight: bold;
      }
    }
  }

  // 树形控件样式优化
  :deep(.el-tree) {
    .el-tree-node__content {
      height: 32px;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background-color: #e6f7ff;
      color: #409EFF;
    }
  }

  // 标签样式优化
  :deep(.el-check-tag) {
    margin-right: 8px;
    margin-bottom: 6px;
    border-radius: 4px;

    &.is-checked {
      background-color: #409EFF;
      border-color: #409EFF;
      color: #fff;
    }
  }

  // 表格样式优化
  :deep(.im-table) {
    .el-table {
      border-radius: 4px;
      overflow: hidden;
    }

    .el-table__header {
      background-color: #fafafa;
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  // 搜索框样式
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 4px;
    }
  }

  // 按钮样式
  :deep(.el-button) {
    border-radius: 4px;

    &.is-circle {
      border-radius: 50%;
    }
  }

  // 日期选择器样式
  :deep(.el-date-editor) {
    border-radius: 4px;
  }

  // 下拉菜单样式
  :deep(.el-dropdown) {
    .el-button {
      border-radius: 4px;
    }
  }
}
</style>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch,
  h,
  CSSProperties,
  markRaw
} from "vue";
import dayjs from "dayjs";
import { Refresh, Download } from "@element-plus/icons-vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";

// 导入API接口
import {
  queryEnergyEquipmentList,
  queryEquipmentCategoryTree,
  getUserDeptTreeAxios,
  exportEnergyEquipmentData,
  deleteEnergyEquipment,
  queryEquipmentStatistics
} from "../api/index";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const categoryTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const deptTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const tableRef = ref<ImTableInstance>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "categoryName",
  children: "children",
  isLeaf: "leaf"
};

// 设备分类数据
const categoryData = ref<any[]>([]);
// 组织数据
const deptData = ref<any[]>([]);

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

// 动态高度
const tmpWidth = ref(0);
const filterTagRef = ref<HTMLElement | null>(null);
const tableKey = ref(0);

// 计算表格高度的函数
const calculateTableHeight = () => {
  nextTick(() => {
    nextTick(() => {
      // 获取筛选区域的高度
      if (!filterTagRef.value) return;
      const filterHeight = filterTagRef.value.offsetHeight;

      // 计算总高度
      tmpWidth.value = filterHeight + 40; // 额外的间距

      // 计算表格高度
      tableHeight.value =
        document.documentElement.offsetHeight - 350 - tmpWidth.value;
      tableOption.height = tableHeight.value;

      tableKey.value = tableKey.value + 1;
    });
  });
};

//根据页面高度设置表格高度
const tableHeight = ref(0);

interface TableColumnType {
  hide: boolean;
  prop: string;
  label: string;
  fieldType: string;
  width: string | number;
  unit?: string;
  component?: any;
  filters?: boolean;
  sortable?: boolean;
  [key: string]: any;
}

interface TableOptionType {
  align: string;
  menuAlign: string;
  border: boolean;
  stripe: boolean;
  height?: number;
  rowKey: string;
  column: TableColumnType[];
  [key: string]: any;
}

const tableOption = reactive<TableOptionType>({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 180,
  height: tableHeight.value,
  rowKey: "equipmentId",
  column: []
});
