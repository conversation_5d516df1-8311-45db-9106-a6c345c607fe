<template>
  <div class="energy-equipment-home">
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <!-- 使用el-tabs组件，设置默认激活的标签页 -->
        <el-tabs v-model="activeTabName" class="ml-2 mr-2" @tab-change="viewTabChangeHandler">
          <!-- 设备分类视图标签页 -->
          <el-tab-pane name="categoryView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-Settings3Fill" />
                <span class="ml-1">设备分类</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索设备分类 -->
              <div class="pb-1.5 flex items-center">
                <el-input
                  placeholder="设备分类名称"
                  :suffix-icon="useRenderIcon('EP-Search')"
                  clearable
                  v-model="categoryKeyWord"
                />
                <el-icon @click="loadCategoryData" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示设备分类结构 -->
              <el-tree
                ref="categoryTreeRef"
                :data="categoryData"
                node-key="id"
                :expand-on-click-node="false"
                :props="{
                  label: 'categoryName',
                  children: 'children'
                }"
                highlight-current
                default-expand-all
                :filter-node-method="filterCategoryNode"
                :style="treeStyle"
                @current-change="categoryChangeHandler"
              >
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.equipmentCount || 0) > 0 ? '#409EFF' : 'unset'
                  }">{{ node.label }}
                    <span v-if="data.equipmentCount > 0" style="font-size: 12px; color: #909399;">
                      ({{ data.equipmentCount }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>

          <!-- 组织视图标签页 -->
          <el-tab-pane name="deptView">
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-OrganizationChart" />
                <span class="ml-1">组织视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索组织 -->
              <div class="pb-1.5 flex items-center">
                <el-input
                  placeholder="组织名称"
                  :suffix-icon="useRenderIcon('EP-Search')"
                  clearable
                  v-model="deptKeyWord"
                />
                <el-icon @click="loadDeptData" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 组织树形控件 -->
              <el-tree
                ref="deptTreeRef"
                :data="deptData"
                node-key="deptId"
                :expand-on-click-node="false"
                :props="{
                  label: 'deptName',
                  children: 'children'
                }"
                highlight-current
                default-expand-all
                :filter-node-method="filterDeptNode"
                :style="treeStyle"
                @current-change="deptChangeHandler"
              >
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.equipmentCount || 0) > 0 ? '#409EFF' : 'unset'
                  }">{{ node.label }}
                    <span v-if="data.equipmentCount > 0" style="font-size: 12px; color: #909399;">
                      ({{ data.equipmentCount }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>

      <!-- 右侧面板 -->
      <template #paneR>
        <!-- 搜索区域 -->
        <div class="flex justify-center items-center mb-6 mr-6">
          <search-with-column
            v-model="columnCondition.value"
            v-model:fuzzy-enable="columnCondition.fuzzyable"
            v-model:column-val="columnCondition.field"
            :column-options="columnSearchOptions"
            :column-select-width="90"
            @search="resetTablePageAndQuery('', 'toggleQueryCriteria')"
            @reset="resetSearchHandler"
            class="flex-c w-4/5"
            input-class-name="w-3/5"
          />
        </div>

        <!-- 设备状态筛选 -->
        <div class="flex mt-6 mb-2">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">设备状态:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag
                class="mr-2 tag-container"
                :checked="state.statusCheckAll"
                @change="statusChangeHandler(null)"
              >
                全部
              </el-check-tag>
              <el-check-tag
                style="margin-bottom: 6px"
                v-for="item in state.statusData"
                :key="item.value"
                :checked="!state.statusCheckAll && item.checked"
                class="mr-2"
                @change="statusChangeHandler(item)"
              >
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 设备类型筛选 -->
        <div class="flex mt-2 mb-3">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">设备类型:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag
                class="mr-2 tag-container"
                :checked="state.typeCheckAll"
                @change="typeChangeHandler(null)"
              >
                全部
              </el-check-tag>
              <el-check-tag
                style="margin-bottom: 6px"
                v-for="item in state.typeData"
                :key="item.value"
                :checked="!state.typeCheckAll && item.checked"
                class="mr-2"
                @change="typeChangeHandler(item)"
              >
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 分割线 -->
        <div class="dashed-divider mx-4 my-1 pr-6"></div>

        <!-- 表格 -->
        <div class="ml-2 mr-2">
          <im-table
            ref="tableRef"
            :data="tableData"
            center
            toolbar
            :table-alert="{
              closable: false
            }"
            operator
            :height="tableOption.height"
            :stripe="tableOption.stripe"
            show-checkbox
            :column-storage="createColumnStorage('energy-equipment-management', 'remote')"
            :pagination="tablePage"
            :loading="tableLoading"
            :filter-data-provider="filterDataProvider"
            @on-reload="resetTablePageAndQuery"
            @selection-change="selectionChangeHandler"
            @on-page-change="queryEquipmentData"
          >
            <!-- 自定义 alert 提示内容 -->
            <template #tip>
              <span class="statistics-tip" style="margin-left: 100px">
                <span class="statistics-item mr-2 ml-2" v-for="(item, index) in equipmentStatisticsData"
                  :key="index">
                  <span class="label">{{ item.label }}: </span>
                  <span class="count text-[16px] font-bold ml-1">{{ item.value || 0 }}</span>
                </span>
              </span>
            </template>

            <!-- 表格右侧菜单 -->
            <template #toolbar-right="{ size }">
              <div class="float-left flex-sc pr-3 gap-3">
                <!-- 日期选择器 -->
                <el-date-picker
                  clearable
                  v-model="dateTimeRange"
                  type="daterange"
                  range-separator="到"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 200px"
                  @change="dateChangeFunction"
                />

                <!-- 批量操作 -->
                <el-dropdown style="margin: 0 10px">
                  <el-button type="primary"> 批量操作 </el-button>
                  <template #dropdown>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        :disabled="state.selectedEquipmentRows.length == 0"
                        style="padding: 0.4rem 1rem"
                        @click.native="handleBatchOperation('export')"
                      >
                        批量导出
                      </el-dropdown-item>
                      <el-dropdown-item
                        style="padding: 0.4rem 1rem"
                        :disabled="state.selectedEquipmentRows.length == 0"
                        @click="handleBatchOperation('delete')"
                        size="small"
                        type="primary"
                      >
                        批量删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <!-- 导出数据 -->
                <el-tooltip content="导出数据" placement="top" :open-delay="1000">
                  <el-button
                    style="margin-left: 0.1rem"
                    :icon="useRenderIcon('EP-Download')"
                    circle
                    :size="size"
                    :disabled="tableData.length == 0"
                    @click="exportEquipmentHandler"
                  />
                </el-tooltip>

                <!-- 新增设备 -->
                <el-tooltip content="新增设备" placement="top" :open-delay="1000">
                  <el-button
                    style="margin-left: 0.1rem"
                    :icon="useRenderIcon('EP-Plus')"
                    circle
                    :size="size"
                    type="primary"
                    @click="addEquipmentHandler"
                  />
                </el-tooltip>
              </div>
            </template>

            <!-- 表格操作按钮 -->
            <template #operator="{ row, size }">
              <!-- 详情按钮 -->
              <el-button
                :size="size"
                type="primary"
                text
                :icon="useRenderIcon('EP-View')"
                @click="detailViewHandler(row)"
              >
                详情
              </el-button>
              <!-- 编辑按钮 -->
              <el-button
                :size="size"
                type="warning"
                text
                :icon="useRenderIcon('EP-Edit')"
                @click="editEquipmentHandler(row)"
              >
                编辑
              </el-button>
              <!-- 删除按钮 -->
              <el-button
                :size="size"
                type="danger"
                text
                :icon="useRenderIcon('EP-Delete')"
                @click="deleteEquipmentHandler(row)"
              >
                删除
              </el-button>
            </template>

            <!-- 设备状态列 -->
            <template #status="{ row }">
              <el-tag :type="getStatusType(row.status)" effect="light">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>

            <!-- 设备类型列 -->
            <template #equipmentType="{ row }">
              <el-tag>{{ row.equipmentType }}</el-tag>
            </template>

            <!-- 能耗等级列 -->
            <template #energyLevel="{ row }">
              <el-tag :color="getEnergyLevelColor(row.energyLevel)" class="text-white border-none">
                {{ getEnergyLevelLabel(row.energyLevel) }}
              </el-tag>
            </template>
          </im-table>
        </div>
      </template>
    </splitpane>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch,
  h,
  CSSProperties,
  markRaw
} from "vue";
import dayjs from "dayjs";
import { Refresh, Download } from "@element-plus/icons-vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";

// 导入API接口
import {
  queryEnergyEquipmentList,
  queryEquipmentCategoryTree,
  getUserDeptTreeAxios,
  exportEnergyEquipmentData,
  deleteEnergyEquipment,
  queryEquipmentStatistics
} from "../api/index";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const categoryTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const deptTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const tableRef = ref<ImTableInstance>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "categoryName",
  children: "children",
  isLeaf: "leaf"
};

// 设备分类数据
const categoryData = ref<any[]>([]);
// 组织数据
const deptData = ref<any[]>([]);

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

// 动态高度
const tmpWidth = ref(0);
const filterTagRef = ref<HTMLElement | null>(null);
const tableKey = ref(0);

// 计算表格高度的函数
const calculateTableHeight = () => {
  nextTick(() => {
    nextTick(() => {
      // 获取筛选区域的高度
      if (!filterTagRef.value) return;
      const filterHeight = filterTagRef.value.offsetHeight;

      // 计算总高度
      tmpWidth.value = filterHeight + 40; // 额外的间距

      // 计算表格高度
      tableHeight.value =
        document.documentElement.offsetHeight - 350 - tmpWidth.value;
      tableOption.height = tableHeight.value;

      tableKey.value = tableKey.value + 1;
    });
  });
};

//根据页面高度设置表格高度
const tableHeight = ref(0);

interface TableColumnType {
  hide: boolean;
  prop: string;
  label: string;
  fieldType: string;
  width: string | number;
  unit?: string;
  component?: any;
  filters?: boolean;
  sortable?: boolean;
  [key: string]: any;
}

interface TableOptionType {
  align: string;
  menuAlign: string;
  border: boolean;
  stripe: boolean;
  height?: number;
  rowKey: string;
  column: TableColumnType[];
  [key: string]: any;
}

const tableOption = reactive<TableOptionType>({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 180,
  height: tableHeight.value,
  rowKey: "equipmentId",
  column: []
});
