<template>
  <div class="network-device-monitor">
    <!-- 左侧组织架构 -->
    <div class="left-sidebar">
      <div class="org-header">
        <h3>所属组织</h3>
      </div>
      <div class="org-tree">
        <el-input
          v-model="orgSearchKeyword"
          placeholder="请输入"
          prefix-icon="Search"
          size="small"
          style="margin-bottom: 12px;"
        />
        <el-tree
          :data="orgTreeData"
          :props="{ children: 'children', label: 'name' }"
          node-key="id"
          :default-expanded-keys="['1']"
          :highlight-current="true"
          @node-click="handleOrgNodeClick"
        />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- Tab切换 -->
      <div class="tab-container">
        <div class="tab-header">
          <div
            v-for="tab in tabConfigs"
            :key="tab.key"
            :class="['tab-item', { active: activeTab === tab.key }]"
            @click="switchTab(tab.key)"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>

      <!-- 统计卡片区域 -->
      <div class="statistics-section">
        <div
          v-for="(stat, index) in currentStatistics"
          :key="index"
          class="stat-card"
        >
          <div class="stat-header">
            <span class="stat-title">{{ stat.title }}</span>
            <i class="el-icon-question" v-if="stat.title.includes('超7天')"></i>
          </div>
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-footer">
            <div v-if="stat.trend" class="stat-trend">
              <span class="trend-label">{{ stat.trend.period }}:</span>
              <span :class="['trend-value', stat.trend.type]">
                {{ stat.trend.type === 'up' ? '+' : '-' }}{{ stat.trend.value }}
              </span>
            </div>
            <div v-if="stat.chart" class="stat-chart">
              <!-- 这里可以集成小型图表组件 -->
              <div class="mini-chart" :style="{ backgroundColor: stat.chart.color }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势图表区域 -->
      <div class="chart-section">
        <div class="chart-header">
          <h3>{{ currentTrendChart.title }}</h3>
          <div class="chart-filters">
            <el-select v-model="chartFilters.platform" placeholder="安全运维平台" size="small">
              <el-option
                v-for="option in currentTrendChart.filters.platform"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.location" placeholder="张三" size="small">
              <el-option
                v-for="option in currentTrendChart.filters.location"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.timeRange" placeholder="东莞中心医院" size="small">
              <el-option
                v-for="option in currentTrendChart.filters.timeRange"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.period" placeholder="近24小时" size="small">
              <el-option label="近24小时" value="24h" />
              <el-option label="近7天" value="7d" />
              <el-option label="近30天" value="30d" />
            </el-select>
          </div>
        </div>
        <div class="chart-content">
          <!-- 这里集成ECharts图表组件 -->
          <div class="trend-chart" ref="trendChartRef"></div>
        </div>
      </div>

      <!-- 设备列表区域 -->
      <div class="device-list-section">
        <div class="list-header">
          <h3>{{ activeTab === 'server' ? '服务器列表' : '终端列表' }}</h3>
          <div class="list-actions">
            <el-select v-model="tableFilters.searchKeyword" placeholder="请选择" size="small">
              <el-option label="全部" value="" />
            </el-select>
            <el-button type="primary" size="small">查询</el-button>
            <el-button size="small">重置</el-button>
          </div>
        </div>

        <div class="action-buttons">
          <el-button type="primary" size="small">
            <i class="el-icon-plus"></i>
            新增资产
          </el-button>
          <el-button size="small">批量制除</el-button>
          <el-button size="small">任务配置</el-button>
          <el-button size="small">导出</el-button>
        </div>

        <!-- 设备表格 -->
        <el-table
          :data="currentDeviceList"
          v-loading="loading"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <div class="status-indicator">
                <span :class="['status-dot', getStatusClass(scope.row.status)]"></span>
                {{ getStatusText(scope.row.status) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ipAddress" label="IP地址" width="140"></el-table-column>
          <el-table-column prop="macAddress" label="MAC地址" width="140"></el-table-column>

          <!-- 服务器设备特有列 -->
          <el-table-column
            v-if="activeTab === 'server'"
            prop="businessSystem"
            label="所属业务系统"
            width="140"
          ></el-table-column>

          <el-table-column prop="assetOwner" label="资产责任人" width="100"></el-table-column>
          <el-table-column prop="location" label="所在位置" width="120"></el-table-column>

          <!-- 根据Tab显示不同的时长列 -->
          <el-table-column
            :prop="activeTab === 'server' ? 'onlineDuration' : 'nearestOnlineTime'"
            :label="activeTab === 'server' ? '自动在线时长（小时）' : '近一月自动在线时长'"
            width="160"
          ></el-table-column>

          <!-- 终端设备特有列 -->
          <template v-if="activeTab === 'terminal'">
            <el-table-column prop="lastOnlineTime" label="最近一次在线时间" width="160"></el-table-column>
            <el-table-column prop="lastOnlineDuration" label="最近一次在线时长" width="140"></el-table-column>
          </template>

          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                v-for="operation in scope.row.operations"
                :key="operation"
                size="small"
                type="primary"
                link
                @click="handleOperation(operation, scope.row)"
              >
                {{ operation }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import {
  DeviceTabType,
  DeviceStatus,
  StatisticCard,
  TrendChartConfig,
  ServerDevice,
  TerminalDevice,
  TableFilters,
  PaginationInfo,
  HomePageState
} from '../types';
import apiService from '../api';

// 响应式数据
const state = reactive<HomePageState>({
  activeTab: DeviceTabType.SERVER,
  serverDevices: [],
  terminalDevices: [],
  tableFilters: {
    searchKeyword: '',
    platform: '',
    location: '',
    timeRange: ''
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  },
  loading: false
});

// 其他响应式数据
const orgSearchKeyword = ref('');
const chartFilters = reactive({
  platform: '',
  location: '',
  timeRange: '',
  period: '24h'
});

// 统计数据
const serverStatistics = ref<StatisticCard[]>([]);
const terminalStatistics = ref<StatisticCard[]>([]);
const serverTrendChart = ref<TrendChartConfig>({} as TrendChartConfig);
const terminalTrendChart = ref<TrendChartConfig>({} as TrendChartConfig);

// 图表引用
const trendChartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 组织架构数据
const orgTreeData = ref([
  {
    id: '1',
    name: '东莞市中心医院',
    children: [
      { id: '1-1', name: '东莞中心医院总院' },
      { id: '1-2', name: '东莞中心医院分院' }
    ]
  }
]);

// Tab配置
const tabConfigs = [
  { key: DeviceTabType.SERVER, label: '服务器设备' },
  { key: DeviceTabType.TERMINAL, label: '终端设备' }
];

// 计算属性
const currentStatistics = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverStatistics.value : terminalStatistics.value;
});

const currentTrendChart = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverTrendChart.value : terminalTrendChart.value;
});

const currentDeviceList = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? state.serverDevices : state.terminalDevices;
});

// 方法定义
const switchTab = async (tabKey: DeviceTabType) => {
  state.activeTab = tabKey;
  await loadDeviceList();
  await nextTick();
  initChart();
};

const handleOrgNodeClick = (data: any) => {
  console.log('选中组织:', data);
  loadDeviceList();
};

const getStatusClass = (status: DeviceStatus) => {
  const statusMap = {
    [DeviceStatus.ONLINE]: 'online',
    [DeviceStatus.OFFLINE]: 'offline',
    [DeviceStatus.FAULT]: 'fault',
    [DeviceStatus.MAINTENANCE]: 'maintenance'
  };
  return statusMap[status] || 'offline';
};

const getStatusText = (status: DeviceStatus) => {
  const statusMap = {
    [DeviceStatus.ONLINE]: '在线',
    [DeviceStatus.OFFLINE]: '离线',
    [DeviceStatus.FAULT]: '故障',
    [DeviceStatus.MAINTENANCE]: '维护中'
  };
  return statusMap[status] || '未知';
};

const handleSelectionChange = (selection: any[]) => {
  console.log('选中设备:', selection);
};

const handleSizeChange = (size: number) => {
  state.pagination.pageSize = size;
  loadDeviceList();
};

const handleCurrentChange = (page: number) => {
  state.pagination.current = page;
  loadDeviceList();
};

const handleOperation = async (operation: string, device: any) => {
  try {
    switch (operation) {
      case '查看':
        await apiService.operation.viewDevice(device.id);
        ElMessage.success('查看设备详情');
        break;
      case '在线状态':
        await apiService.operation.toggleOnlineStatus(device.id);
        ElMessage.success('状态切换成功');
        loadDeviceList();
        break;
      case '移除':
        await apiService.operation.removeDevice(device.id);
        ElMessage.success('设备移除成功');
        loadDeviceList();
        break;
    }
  } catch (error) {
    ElMessage.error('操作失败');
  }
};

// 数据加载方法
const loadStatistics = async () => {
  try {
    const [serverStats, terminalStats] = await Promise.all([
      apiService.server.getStatistics(),
      apiService.terminal.getStatistics()
    ]);

    if (serverStats.success) {
      serverStatistics.value = serverStats.data;
    }

    if (terminalStats.success) {
      terminalStatistics.value = terminalStats.data;
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败');
  }
};

const loadTrendCharts = async () => {
  try {
    const [serverChart, terminalChart] = await Promise.all([
      apiService.server.getTrendChart(),
      apiService.terminal.getTrendChart()
    ]);

    if (serverChart.success) {
      serverTrendChart.value = serverChart.data;
    }

    if (terminalChart.success) {
      terminalTrendChart.value = terminalChart.data;
    }
  } catch (error) {
    ElMessage.error('加载图表数据失败');
  }
};

const loadDeviceList = async () => {
  state.loading = true;
  try {
    const api = state.activeTab === DeviceTabType.SERVER ? apiService.server : apiService.terminal;
    const response = await api.getDeviceList(state.tableFilters, state.pagination);

    if (response.success) {
      if (state.activeTab === DeviceTabType.SERVER) {
        state.serverDevices = response.data.list;
      } else {
        state.terminalDevices = response.data.list;
      }
      state.pagination = response.data.pagination;
    }
  } catch (error) {
    ElMessage.error('加载设备列表失败');
  } finally {
    state.loading = false;
  }
};

// 图表初始化
const initChart = () => {
  if (!trendChartRef.value || !currentTrendChart.value.data) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(trendChartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['在线数量', '总数量']
    },
    xAxis: {
      type: 'category',
      data: currentTrendChart.value.data.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '在线数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.onlineCount),
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '总数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.totalCount),
        itemStyle: { color: '#1890ff' }
      }
    ]
  };

  chartInstance.setOption(option);
};

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadStatistics(),
    loadTrendCharts(),
    loadDeviceList()
  ]);

  await nextTick();
  initChart();
});
</script>

<style scoped lang="scss">
.network-device-monitor {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;

  .left-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e4e7ed;
    padding: 16px;

    .org-header {
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .org-tree {
      .el-tree {
        background: transparent;
      }
    }
  }

  .main-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .tab-container {
      margin-bottom: 16px;

      .tab-header {
        display: flex;
        background: white;
        border-radius: 4px;
        padding: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .tab-item {
          flex: 1;
          text-align: center;
          padding: 8px 16px;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.3s ease;
          color: #606266;
          font-weight: 500;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #409eff;
            color: white;
          }
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 16px;

      .stat-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .stat-title {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }

          .el-icon-question {
            color: #909399;
            cursor: pointer;
          }
        }

        .stat-value {
          font-size: 32px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 8px;
        }

        .stat-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .stat-trend {
            font-size: 12px;

            .trend-label {
              color: #909399;
            }

            .trend-value {
              font-weight: 600;
              margin-left: 4px;

              &.up {
                color: #67c23a;
              }

              &.down {
                color: #f56c6c;
              }
            }
          }

          .stat-chart {
            .mini-chart {
              width: 60px;
              height: 20px;
              border-radius: 2px;
              opacity: 0.3;
            }
          }
        }
      }
    }

    .chart-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .chart-filters {
          display: flex;
          gap: 12px;

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-content {
        .trend-chart {
          width: 100%;
          height: 300px;
        }
      }
    }

    .device-list-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .list-actions {
          display: flex;
          gap: 12px;
          align-items: center;

          .el-select {
            width: 120px;
          }
        }
      }

      .action-buttons {
        margin-bottom: 16px;
        display: flex;
        gap: 8px;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.online {
            background-color: #67c23a;
          }

          &.offline {
            background-color: #909399;
          }

          &.fault {
            background-color: #f56c6c;
          }

          &.maintenance {
            background-color: #e6a23c;
          }
        }
      }

      .pagination-wrapper {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .network-device-monitor {
    .main-content {
      .statistics-section {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .network-device-monitor {
    flex-direction: column;

    .left-sidebar {
      width: 100%;
      height: auto;
    }

    .main-content {
      .statistics-section {
        grid-template-columns: 1fr;
      }

      .chart-section .chart-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .chart-filters {
          justify-content: center;
        }
      }

      .device-list-section .list-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }
    }
  }
}
</style>
