<template>
  <div class="network-device-monitor">
    <!-- 左侧组织架构 -->
    <div class="left-sidebar">
      <div class="org-header">
        <h3>所属组织</h3>
      </div>
      <div class="org-tree">
        <el-input
          v-model="orgSearchKeyword"
          placeholder="请输入"
          prefix-icon="Search"
          size="small"
        />
        <el-tree
          :data="orgTreeData"
          :props="{ children: 'children', label: 'name' }"
          node-key="id"
          :default-expanded-keys="['1']"
          @node-click="handleOrgNodeClick"
        />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- Tab切换 - 按原型图样式 -->
      <div class="tab-container">
        <div
          v-for="tab in tabConfigs"
          :key="tab.key"
          :class="['tab-item', { active: state.activeTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </div>
      </div>

      <!-- 统计卡片区域 -->
      <div class="statistics-section">
        <div
          v-for="(stat, index) in currentStatistics"
          :key="index"
          class="stat-card"
        >
          <div class="stat-header">
            <span class="stat-title">{{ stat.title }}</span>
            <el-icon v-if="stat.title.includes('超7天')" class="help-icon">
              <QuestionFilled />
            </el-icon>
          </div>
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-footer">
            <div v-if="stat.trend" class="stat-trend">
              <span class="trend-label">{{ stat.trend.period }}:</span>
              <span :class="['trend-value', stat.trend.type]">
                {{ stat.trend.type === 'up' ? '+' : '' }}{{ stat.trend.value }}
              </span>
            </div>
            <div v-if="stat.chart" class="stat-chart">
              <div class="mini-chart" :style="{ backgroundColor: stat.chart.color }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势图表区域 -->
      <div class="chart-section">
        <div class="chart-header">
          <h3>{{ currentTrendChart.title }}</h3>
          <div class="chart-filters">
            <el-select v-model="chartFilters.platform" placeholder="安全运维平台" size="small">
              <el-option
                v-for="option in currentTrendChart.filters.platform"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.location" placeholder="张三" size="small">
              <el-option
                v-for="option in currentTrendChart.filters.location"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.timeRange" placeholder="东莞中心医院" size="small">
              <el-option
                v-for="option in currentTrendChart.filters.timeRange"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.period" placeholder="近24小时" size="small">
              <el-option label="近24小时" value="24h" />
              <el-option label="近7天" value="7d" />
              <el-option label="近30天" value="30d" />
            </el-select>
          </div>
        </div>
        <div class="chart-content">
          <!-- 这里集成ECharts图表组件 -->
          <div class="trend-chart" ref="trendChartRef"></div>
        </div>
      </div>

      <!-- 设备列表区域 -->
      <div class="device-list-section">
        <div class="list-header">
          <h3>{{ state.activeTab === 'server' ? '服务器列表' : '终端列表' }}</h3>
          <div class="list-actions">
            <el-input
              v-model="state.tableFilters.searchKeyword"
              placeholder="请输入关键词搜索"
              size="small"
              style="width: 200px; margin-right: 8px;"
              clearable
            />
            <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
            <el-button size="small" @click="handleReset">重置</el-button>
          </div>
        </div>

        <div class="action-buttons">
          <el-button type="primary" size="small">
            <el-icon><Plus /></el-icon>
            新增资产
          </el-button>
          <el-button size="small">批量删除</el-button>
          <el-button size="small">任务配置</el-button>
          <el-button size="small">导出</el-button>
        </div>

        <!-- 设备表格 -->
        <el-table
          :data="currentDeviceList"
          v-loading="state.loading"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="ipAddress" label="IP地址" width="140"></el-table-column>
          <el-table-column prop="macAddress" label="MAC地址" width="140"></el-table-column>

          <!-- 服务器设备特有列 -->
          <el-table-column
            v-if="state.activeTab === 'server'"
            prop="businessSystem"
            label="所属业务系统"
            width="140"
          ></el-table-column>

          <el-table-column prop="assetOwner" label="资产责任人" width="100"></el-table-column>
          <el-table-column prop="location" label="所在位置" width="120"></el-table-column>

          <!-- 根据Tab显示不同的时长列 -->
          <el-table-column
            :prop="state.activeTab === 'server' ? 'onlineDuration' : 'nearestOnlineTime'"
            :label="state.activeTab === 'server' ? '自动在线时长（小时）' : '近一月自动在线时长'"
            width="160"
          ></el-table-column>

          <!-- 终端设备特有列 -->
          <template v-if="state.activeTab === 'terminal'">
            <el-table-column prop="lastOnlineTime" label="最近一次在线时间" width="160"></el-table-column>
            <el-table-column prop="lastOnlineDuration" label="最近一次在线时长" width="140"></el-table-column>
          </template>

          <!-- 状态列放在最后 -->
          <el-table-column prop="status" label="在线状态" width="100">
            <template #default="scope">
              <div class="status-indicator">
                <span :class="['status-dot', getStatusClass(scope.row.status)]"></span>
                {{ getStatusText(scope.row.status) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                v-for="operation in scope.row.operations"
                :key="operation"
                size="small"
                type="primary"
                link
                @click="handleOperation(operation, scope.row)"
              >
                {{ operation }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="state.pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="state.pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="state.pagination.total"
          />
        </div>
      </div>
    </div>

    <!-- 设备详情弹窗 -->
    <el-dialog
      v-model="deviceDetailVisible"
      title="设备详情"
      width="80%"
      :before-close="handleCloseDetail"
      class="device-detail-dialog"
    >
      <div v-if="deviceDetailLoading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>
      <div v-else-if="deviceDetail" class="device-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>设备名称：</label>
              <span>{{ deviceDetail.name }}</span>
            </div>
            <div class="info-item">
              <label>设备状态：</label>
              <el-tag :type="getStatusTagType(deviceDetail.status)">
                {{ getStatusText(deviceDetail.status) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>IP地址：</label>
              <span>{{ deviceDetail.ipAddress }}</span>
            </div>
            <div class="info-item">
              <label>MAC地址：</label>
              <span>{{ deviceDetail.macAddress }}</span>
            </div>
            <div class="info-item">
              <label>资产责任人：</label>
              <span>{{ deviceDetail.assetOwner }}</span>
            </div>
            <div class="info-item">
              <label>所在位置：</label>
              <span>{{ deviceDetail.location }}</span>
            </div>
            <div v-if="deviceDetail.businessSystem" class="info-item">
              <label>业务系统：</label>
              <span>{{ deviceDetail.businessSystem }}</span>
            </div>
            <div class="info-item">
              <label>健康评分：</label>
              <el-progress
                :percentage="deviceDetail.healthScore"
                :color="getHealthColor(deviceDetail.healthScore)"
                :show-text="true"
                :format="() => `${deviceDetail.healthScore}分`"
              />
            </div>
          </div>
        </div>

        <!-- 设备规格 -->
        <div class="detail-section">
          <h3 class="section-title">设备规格</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>制造商：</label>
              <span>{{ deviceDetail.manufacturer }}</span>
            </div>
            <div class="info-item">
              <label>型号：</label>
              <span>{{ deviceDetail.model }}</span>
            </div>
            <div class="info-item">
              <label>额定功率：</label>
              <span>{{ deviceDetail.ratedPower }}W</span>
            </div>
            <div class="info-item">
              <label>当前功率：</label>
              <span>{{ deviceDetail.currentPower }}W</span>
            </div>
            <div class="info-item">
              <label>能效等级：</label>
              <el-tag :color="getEnergyLevelColor(deviceDetail.energyLevel)">
                {{ deviceDetail.energyLevel }}级
              </el-tag>
            </div>
            <div class="info-item">
              <label>安装日期：</label>
              <span>{{ deviceDetail.installDate }}</span>
            </div>
            <div class="info-item">
              <label>保修到期：</label>
              <span>{{ deviceDetail.warrantyEndDate }}</span>
            </div>
          </div>
        </div>

        <!-- 运行状态 -->
        <div class="detail-section">
          <h3 class="section-title">运行状态</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>上线时间：</label>
              <span>{{ deviceDetail.onlineTime || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>离线时间：</label>
              <span>{{ deviceDetail.offlineTime || '当前在线' }}</span>
            </div>
            <div class="info-item">
              <label>累计在线时长：</label>
              <span>{{ Math.floor(deviceDetail.totalOnlineHours / 24) }}天{{ deviceDetail.totalOnlineHours % 24 }}小时</span>
            </div>
            <div class="info-item">
              <label>上次维护：</label>
              <span>{{ deviceDetail.lastMaintenanceDate }}</span>
            </div>
            <div class="info-item">
              <label>下次维护：</label>
              <span>{{ deviceDetail.nextMaintenanceDate }}</span>
            </div>
          </div>
        </div>

        <!-- 设备描述 -->
        <div class="detail-section">
          <h3 class="section-title">设备描述</h3>
          <p class="device-description">{{ deviceDetail.description }}</p>
        </div>

        <!-- 历史数据图表 -->
        <div class="detail-section">
          <h3 class="section-title">近7天功耗趋势</h3>
          <div class="history-chart" ref="historyChartRef"></div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDetail">关闭</el-button>
          <el-button type="primary" @click="handleEditDevice">编辑设备</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { QuestionFilled, Plus } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import {
  DeviceTabType,
  DeviceStatus,
  StatisticCard,
  TrendChartConfig,
  HomePageState,
  OrgTreeNode,
  DeviceDetail
} from '../types';
import apiService from '../api';

// 响应式数据
const state = reactive<HomePageState>({
  activeTab: DeviceTabType.SERVER,
  serverDevices: [],
  terminalDevices: [],
  tableFilters: {
    searchKeyword: ''
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  },
  loading: false
});

// 其他响应式数据
const orgSearchKeyword = ref('');
const chartFilters = reactive({
  platform: '',
  location: '',
  timeRange: '',
  period: '24h'
});

// 统计数据
const serverStatistics = ref<StatisticCard[]>([]);
const terminalStatistics = ref<StatisticCard[]>([]);
const serverTrendChart = ref<TrendChartConfig>({} as TrendChartConfig);
const terminalTrendChart = ref<TrendChartConfig>({} as TrendChartConfig);

// 图表引用
const trendChartRef = ref<HTMLElement>();
const historyChartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;
let historyChartInstance: echarts.ECharts | null = null;

// 设备详情弹窗相关
const deviceDetailVisible = ref(false);
const deviceDetailLoading = ref(false);
const deviceDetail = ref<DeviceDetail | null>(null);

// 组织架构数据
const orgTreeData = ref<OrgTreeNode[]>([
  {
    id: '1',
    name: '东莞市中心医院',
    children: [
      { id: '1-1', name: '东莞中心医院总院' },
      { id: '1-2', name: '东莞中心医院分院' }
    ]
  }
]);

// Tab配置
const tabConfigs = [
  { key: DeviceTabType.SERVER, label: '服务器设备' },
  { key: DeviceTabType.TERMINAL, label: '终端设备' }
];

// 计算属性
const currentStatistics = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverStatistics.value : terminalStatistics.value;
});

const currentTrendChart = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverTrendChart.value : terminalTrendChart.value;
});

const currentDeviceList = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? state.serverDevices : state.terminalDevices;
});

// 方法定义
const switchTab = async (tabKey: DeviceTabType) => {
  state.activeTab = tabKey;
  await loadDeviceList();
  await nextTick();
  initChart();
};

const handleOrgNodeClick = (data: OrgTreeNode) => {
  loadDeviceList();
};

const getStatusClass = (status: DeviceStatus) => ({
  [DeviceStatus.ONLINE]: 'online',
  [DeviceStatus.OFFLINE]: 'offline',
  [DeviceStatus.FAULT]: 'fault',
  [DeviceStatus.MAINTENANCE]: 'maintenance'
}[status] || 'offline');

const getStatusText = (status: DeviceStatus) => ({
  [DeviceStatus.ONLINE]: '在线',
  [DeviceStatus.OFFLINE]: '离线',
  [DeviceStatus.FAULT]: '故障',
  [DeviceStatus.MAINTENANCE]: '维护中'
}[status] || '未知');

const handleSelectionChange = (selection: any[]) => {
  // 处理选择变化
};

const handleSizeChange = (size: number) => {
  state.pagination.pageSize = size;
  loadDeviceList();
};

const handleCurrentChange = (page: number) => {
  state.pagination.current = page;
  loadDeviceList();
};

const handleSearch = () => {
  state.pagination.current = 1; // 重置到第一页
  loadDeviceList();
};

const handleReset = () => {
  state.tableFilters.searchKeyword = '';
  state.pagination.current = 1;
  loadDeviceList();
};

const handleOperation = async (operation: string, device: any) => {
  try {
    if (operation === '查看') {
      await showDeviceDetail(device.id);
      return;
    }

    const actions = {
      '在线状态': () => apiService.operation.toggleOnlineStatus(device.id),
      '移除': () => apiService.operation.removeDevice(device.id)
    };

    await actions[operation]?.();
    ElMessage.success(`${operation}成功`);
    loadDeviceList();
  } catch (error) {
    ElMessage.error('操作失败');
  }
};

// 设备详情弹窗相关方法
const showDeviceDetail = async (deviceId: string) => {
  deviceDetailVisible.value = true;
  deviceDetailLoading.value = true;

  try {
    const response = await apiService.operation.getDeviceDetail(deviceId);
    if (response.success) {
      deviceDetail.value = response.data;
      await nextTick();
      initHistoryChart();
    }
  } catch (error) {
    ElMessage.error('加载设备详情失败');
  } finally {
    deviceDetailLoading.value = false;
  }
};

const handleCloseDetail = () => {
  deviceDetailVisible.value = false;
  deviceDetail.value = null;
  if (historyChartInstance) {
    historyChartInstance.dispose();
    historyChartInstance = null;
  }
};

const handleEditDevice = () => {
  ElMessage.info('编辑功能待开发');
};

const getStatusTagType = (status: DeviceStatus) => {
  const typeMap = {
    [DeviceStatus.ONLINE]: 'success',
    [DeviceStatus.OFFLINE]: 'info',
    [DeviceStatus.FAULT]: 'danger',
    [DeviceStatus.MAINTENANCE]: 'warning'
  };
  return typeMap[status] || 'info';
};

const getHealthColor = (score: number) => {
  if (score >= 90) return '#67c23a';
  if (score >= 80) return '#409eff';
  if (score >= 60) return '#e6a23c';
  return '#f56c6c';
};

const getEnergyLevelColor = (level: string) => {
  const colorMap = {
    'A': '#67c23a',
    'B': '#409eff',
    'C': '#e6a23c',
    'D': '#f56c6c'
  };
  return colorMap[level] || '#909399';
};

// 数据加载方法
const loadStatistics = async () => {
  try {
    const [serverStats, terminalStats] = await Promise.all([
      apiService.server.getStatistics(),
      apiService.terminal.getStatistics()
    ]);

    if (serverStats.success) {
      serverStatistics.value = serverStats.data;
    }

    if (terminalStats.success) {
      terminalStatistics.value = terminalStats.data;
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败');
  }
};

const loadTrendCharts = async () => {
  try {
    const [serverChart, terminalChart] = await Promise.all([
      apiService.server.getTrendChart(),
      apiService.terminal.getTrendChart()
    ]);

    if (serverChart.success) {
      serverTrendChart.value = serverChart.data;
    }

    if (terminalChart.success) {
      terminalTrendChart.value = terminalChart.data;
    }
  } catch (error) {
    ElMessage.error('加载图表数据失败');
  }
};

const loadDeviceList = async () => {
  state.loading = true;
  try {
    const api = state.activeTab === DeviceTabType.SERVER ? apiService.server : apiService.terminal;
    const response = await api.getDeviceList(state.tableFilters, state.pagination);

    if (response.success) {
      if (state.activeTab === DeviceTabType.SERVER) {
        state.serverDevices = response.data.list;
      } else {
        state.terminalDevices = response.data.list;
      }
      state.pagination = response.data.pagination;
    }
  } catch (error) {
    ElMessage.error('加载设备列表失败');
  } finally {
    state.loading = false;
  }
};

// 图表初始化
const initChart = () => {
  if (!trendChartRef.value || !currentTrendChart.value.data) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(trendChartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['在线数量', '总数量']
    },
    xAxis: {
      type: 'category',
      data: currentTrendChart.value.data.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '在线数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.onlineCount),
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '总数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.totalCount),
        itemStyle: { color: '#1890ff' }
      }
    ]
  };

  chartInstance.setOption(option);
};

// 历史数据图表初始化
const initHistoryChart = () => {
  if (!historyChartRef.value || !deviceDetail.value?.historyData) return;

  if (historyChartInstance) {
    historyChartInstance.dispose();
  }

  historyChartInstance = echarts.init(historyChartRef.value);

  const historyData = deviceDetail.value.historyData;
  const dates = historyData.map(item => item.date);
  const powers = historyData.map(item => item.power);
  const onlineHours = historyData.map(item => item.onlineHours);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['功耗(W)', '在线时长(h)']
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisPointer: {
        type: 'shadow'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '功耗(W)',
        position: 'left',
        axisLabel: {
          formatter: '{value} W'
        }
      },
      {
        type: 'value',
        name: '在线时长(h)',
        position: 'right',
        axisLabel: {
          formatter: '{value} h'
        }
      }
    ],
    series: [
      {
        name: '功耗(W)',
        type: 'line',
        data: powers,
        smooth: true,
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '在线时长(h)',
        type: 'bar',
        yAxisIndex: 1,
        data: onlineHours,
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  };

  historyChartInstance.setOption(option);
};

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadStatistics(),
    loadTrendCharts(),
    loadDeviceList()
  ]);

  await nextTick();
  initChart();
});
</script>

<style scoped lang="scss">
.network-device-monitor {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;

  .left-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e4e7ed;
    padding: 16px;

    .org-header {
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .org-tree {
      .el-tree {
        background: transparent;
      }
    }
  }

  .main-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .tab-container {
      display: flex;
      margin-bottom: 16px;
      border-bottom: 1px solid #e4e7ed;

      .tab-item {
        padding: 12px 24px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        color: #606266;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          color: #409eff;
        }

        &.active {
          color: #409eff;
          border-bottom-color: #409eff;
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 16px;

      .stat-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .stat-title {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }

          .el-icon-question {
            color: #909399;
            cursor: pointer;
          }
        }

        .stat-value {
          font-size: 32px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 8px;
        }

        .stat-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .stat-trend {
            font-size: 12px;

            .trend-label {
              color: #909399;
            }

            .trend-value {
              font-weight: 600;
              margin-left: 4px;

              &.up {
                color: #67c23a;
              }

              &.down {
                color: #f56c6c;
              }
            }
          }

          .stat-chart {
            .mini-chart {
              width: 60px;
              height: 20px;
              border-radius: 2px;
              opacity: 0.3;
            }
          }
        }
      }
    }

    .chart-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .chart-filters {
          display: flex;
          gap: 12px;

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-content {
        .trend-chart {
          width: 100%;
          height: 300px;
        }
      }
    }

    .device-list-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .list-actions {
          display: flex;
          gap: 12px;
          align-items: center;

          .el-select {
            width: 120px;
          }
        }
      }

      .action-buttons {
        margin-bottom: 16px;
        display: flex;
        gap: 8px;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.online {
            background-color: #67c23a;
          }

          &.offline {
            background-color: #909399;
          }

          &.fault {
            background-color: #f56c6c;
          }

          &.maintenance {
            background-color: #e6a23c;
          }
        }
      }

      .pagination-wrapper {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .network-device-monitor {
    .main-content {
      .statistics-section {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .network-device-monitor {
    flex-direction: column;

    .left-sidebar {
      width: 100%;
      height: auto;
    }

    .main-content {
      .statistics-section {
        grid-template-columns: 1fr;
      }

      .chart-section .chart-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .chart-filters {
          justify-content: center;
        }
      }

      .device-list-section .list-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }
    }
  }
}

// 设备详情弹窗样式
:deep(.device-detail-dialog) {
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .loading-container {
    padding: 20px;
  }

  .device-detail-content {
    .detail-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            min-width: 120px;
            margin-right: 8px;
          }

          span {
            color: #303133;
          }

          .el-progress {
            flex: 1;
            max-width: 200px;
          }
        }
      }

      .device-description {
        color: #606266;
        line-height: 1.6;
        margin: 0;
        padding: 12px;
        background: #f5f7fa;
        border-radius: 4px;
      }

      .history-chart {
        height: 300px;
        width: 100%;
      }
    }
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 8px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  :deep(.device-detail-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    .device-detail-content {
      .detail-section {
        .info-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
