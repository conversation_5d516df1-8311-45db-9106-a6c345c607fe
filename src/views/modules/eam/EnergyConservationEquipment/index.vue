<template>
  <div class="energy-conservation-equipment">
    <!-- 使用transition组件实现组件切换的过渡效果 -->
    <transition mode="out-in" name="fade-transform">
      <!-- 使用keep-alive包裹动态组件，保持组件状态 -->
      <keep-alive>
        <component
          :is="currentComponent"
          :event-info="selectedEvent"
          @jump-to="comChange"
          @event-select="eventSelectHandler"
          @back="handleBack"
          :needsPassedData="state.needsPassedData"
        />
      </keep-alive>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive, shallowRef, toRefs } from "vue";
import EnergyEquipmentHomePage from "./components/EnergyEquipmentHomePage.vue";
import EnergyEquipmentDetails from "./components/EnergyEquipmentDetails.vue";

const energyEquipmentHome = shallowRef(EnergyEquipmentHomePage);
const energyEquipmentDetail = shallowRef(EnergyEquipmentDetails);

//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 选中的事件
  selectedEvent: null,
  // 需要传递的数据
  needsPassedData: {}
});

const { currentComponent, selectedEvent } = toRefs(state);

// 默认显示主页组件
state.currentComponent = energyEquipmentHome;

// 根据传入的值切换组件
const comChange = (val: string, portableData: Object) => {
  if (val == "energyEquipmentHome") {
    state.currentComponent = energyEquipmentHome;
  } else if (val == "energyEquipmentDetail") {
    state.currentComponent = energyEquipmentDetail;
    state.needsPassedData = portableData; // 传递需要的数据
  }
};

// 处理事件选择
const eventSelectHandler = (evt: any) => {
  state.selectedEvent = evt;
};

// 处理返回事件
const handleBack = () => {
  state.currentComponent = energyEquipmentHome;
};
</script>

<style scoped lang="scss">
.energy-conservation-equipment {
  width: 100%;
  height: 100%;

  // 过渡动画
  .fade-transform-enter-active,
  .fade-transform-leave-active {
    transition: all 0.3s ease;
  }

  .fade-transform-enter-from {
    opacity: 0;
    transform: translateX(30px);
  }

  .fade-transform-leave-to {
    opacity: 0;
    transform: translateX(-30px);
  }
}
</style>