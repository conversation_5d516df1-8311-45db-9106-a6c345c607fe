/**
 * 节能设备管理Mock数据
 */

// 设备分类树数据
export const mockCategoryTreeData = {
  code: 200,
  message: "success",
  data: [
    {
      id: "1",
      categoryName: "空调设备",
      parentId: "0",
      level: 1,
      sort: 1,
      equipmentCount: 15,
      children: [
        {
          id: "1-1",
          categoryName: "中央空调",
          parentId: "1",
          level: 2,
          sort: 1,
          equipmentCount: 8
        },
        {
          id: "1-2",
          categoryName: "分体空调",
          parentId: "1",
          level: 2,
          sort: 2,
          equipmentCount: 7
        }
      ]
    },
    {
      id: "2",
      categoryName: "照明设备",
      parentId: "0",
      level: 1,
      sort: 2,
      equipmentCount: 25,
      children: [
        {
          id: "2-1",
          categoryName: "LED灯具",
          parentId: "2",
          level: 2,
          sort: 1,
          equipmentCount: 20
        },
        {
          id: "2-2",
          categoryName: "节能灯具",
          parentId: "2",
          level: 2,
          sort: 2,
          equipmentCount: 5
        }
      ]
    },
    {
      id: "3",
      categoryName: "供暖设备",
      parentId: "0",
      level: 1,
      sort: 3,
      equipmentCount: 12,
      children: [
        {
          id: "3-1",
          categoryName: "热泵设备",
          parentId: "3",
          level: 2,
          sort: 1,
          equipmentCount: 8
        },
        {
          id: "3-2",
          categoryName: "电暖设备",
          parentId: "3",
          level: 2,
          sort: 2,
          equipmentCount: 4
        }
      ]
    },
    {
      id: "4",
      categoryName: "通风设备",
      parentId: "0",
      level: 1,
      sort: 4,
      equipmentCount: 18,
      children: [
        {
          id: "4-1",
          categoryName: "新风系统",
          parentId: "4",
          level: 2,
          sort: 1,
          equipmentCount: 10
        },
        {
          id: "4-2",
          categoryName: "排风设备",
          parentId: "4",
          level: 2,
          sort: 2,
          equipmentCount: 8
        }
      ]
    }
  ]
};

// 组织架构树数据
export const mockDeptTreeData = {
  code: 200,
  message: "success",
  data: [
    {
      deptId: "1",
      deptName: "总公司",
      parentId: "0",
      level: 1,
      sort: 1,
      equipmentCount: 70,
      children: [
        {
          deptId: "1-1",
          deptName: "技术部",
          parentId: "1",
          level: 2,
          sort: 1,
          equipmentCount: 25
        },
        {
          deptId: "1-2",
          deptName: "行政部",
          parentId: "1",
          level: 2,
          sort: 2,
          equipmentCount: 15
        },
        {
          deptId: "1-3",
          deptName: "生产部",
          parentId: "1",
          level: 2,
          sort: 3,
          equipmentCount: 30
        }
      ]
    }
  ]
};

// 设备列表数据
export const mockEquipmentListData = {
  code: 200,
  message: "success",
  data: {
    list: [
      {
        equipmentId: "EQ001",
        equipmentName: "办公楼中央空调系统",
        equipmentCode: "AC240001",
        equipmentType: "air_conditioning",
        categoryId: "1-1",
        categoryName: "中央空调",
        deptId: "1-2",
        deptName: "行政部",
        location: "办公楼1层",
        status: "running",
        energyLevel: "A",
        ratedPower: 50.5,
        manufacturer: "格力电器",
        model: "GMV-H120WL/A",
        purchaseDate: "2023-03-15",
        installDate: "2023-04-10",
        warrantyEndDate: "2026-04-10",
        description: "办公楼主要制冷制热设备",
        createTime: "2023-04-10 10:30:00",
        updateTime: "2024-01-15 14:20:00",
        createBy: "admin",
        updateBy: "admin"
      },
      {
        equipmentId: "EQ002",
        equipmentName: "生产车间LED照明系统",
        equipmentCode: "LT240002",
        equipmentType: "lighting",
        categoryId: "2-1",
        categoryName: "LED灯具",
        deptId: "1-3",
        deptName: "生产部",
        location: "生产车间A区",
        status: "running",
        energyLevel: "A",
        ratedPower: 15.2,
        manufacturer: "飞利浦",
        model: "LED-T8-18W",
        purchaseDate: "2023-05-20",
        installDate: "2023-06-01",
        warrantyEndDate: "2026-06-01",
        description: "生产车间节能LED照明设备",
        createTime: "2023-06-01 09:15:00",
        updateTime: "2024-01-10 16:45:00",
        createBy: "admin",
        updateBy: "admin"
      },
      {
        equipmentId: "EQ003",
        equipmentName: "会议室分体空调",
        equipmentCode: "AC240003",
        equipmentType: "air_conditioning",
        categoryId: "1-2",
        categoryName: "分体空调",
        deptId: "1-1",
        deptName: "技术部",
        location: "会议室201",
        status: "maintenance",
        energyLevel: "B",
        ratedPower: 3.5,
        manufacturer: "美的",
        model: "KFR-35GW/BP3DN8Y-PH200(B1)",
        purchaseDate: "2023-02-10",
        installDate: "2023-02-25",
        warrantyEndDate: "2026-02-25",
        description: "会议室专用空调设备",
        createTime: "2023-02-25 11:20:00",
        updateTime: "2024-01-20 09:30:00",
        createBy: "admin",
        updateBy: "admin"
      },
      {
        equipmentId: "EQ004",
        equipmentName: "仓库通风系统",
        equipmentCode: "VT240004",
        equipmentType: "ventilation",
        categoryId: "4-1",
        categoryName: "新风系统",
        deptId: "1-3",
        deptName: "生产部",
        location: "仓库B区",
        status: "stopped",
        energyLevel: "C",
        ratedPower: 8.8,
        manufacturer: "松下",
        model: "FV-30VQ3",
        purchaseDate: "2023-01-15",
        installDate: "2023-02-01",
        warrantyEndDate: "2026-02-01",
        description: "仓库通风换气设备",
        createTime: "2023-02-01 14:45:00",
        updateTime: "2024-01-05 10:15:00",
        createBy: "admin",
        updateBy: "admin"
      },
      {
        equipmentId: "EQ005",
        equipmentName: "食堂热泵热水器",
        equipmentCode: "HT240005",
        equipmentType: "heating",
        categoryId: "3-1",
        categoryName: "热泵设备",
        deptId: "1-2",
        deptName: "行政部",
        location: "食堂后厨",
        status: "fault",
        energyLevel: "B",
        ratedPower: 12.0,
        manufacturer: "海尔",
        model: "ES80H-Q1(ZE)",
        purchaseDate: "2023-07-10",
        installDate: "2023-07-25",
        warrantyEndDate: "2026-07-25",
        description: "食堂热水供应设备",
        createTime: "2023-07-25 16:30:00",
        updateTime: "2024-01-25 13:20:00",
        createBy: "admin",
        updateBy: "admin"
      }
    ],
    total: 5,
    pageNum: 1,
    pageSize: 20,
    pages: 1
  }
};

// 设备统计数据
export const mockEquipmentStatistics = {
  code: 200,
  message: "success",
  data: {
    total: 70,
    running: 45,
    stopped: 10,
    maintenance: 8,
    fault: 7,
    energyLevelA: 25,
    energyLevelB: 20,
    energyLevelC: 15,
    energyLevelD: 10,
    totalPower: 1250.5,
    totalConsumption: 8500.2
  }
};

// 设备详情数据
export const mockEquipmentDetail = {
  code: 200,
  message: "success",
  data: {
    equipmentId: "EQ001",
    equipmentName: "办公楼中央空调系统",
    equipmentCode: "AC240001",
    equipmentType: "air_conditioning",
    categoryId: "1-1",
    categoryName: "中央空调",
    deptId: "1-2",
    deptName: "行政部",
    location: "办公楼1层",
    status: "running",
    energyLevel: "A",
    ratedPower: 50.5,
    manufacturer: "格力电器",
    model: "GMV-H120WL/A",
    purchaseDate: "2023-03-15",
    installDate: "2023-04-10",
    warrantyEndDate: "2026-04-10",
    description: "办公楼主要制冷制热设备，采用变频技术，节能效果显著",
    createTime: "2023-04-10 10:30:00",
    updateTime: "2024-01-15 14:20:00",
    createBy: "admin",
    updateBy: "admin"
  }
};

// 能耗数据
export const mockEnergyConsumption = {
  code: 200,
  message: "success",
  data: {
    equipmentId: "EQ001",
    todayConsumption: 125.5,
    monthConsumption: 3850.2,
    yearConsumption: 45600.8,
    avgConsumption: 125.0,
    peakPower: 48.5,
    avgPower: 35.2,
    runningHours: 18.5,
    efficiency: 0.85
  }
};

// 维护记录数据
export const mockMaintenanceRecords = {
  code: 200,
  message: "success",
  data: [
    {
      recordId: "MR001",
      equipmentId: "EQ001",
      maintenanceType: "routine",
      maintenanceDate: "2024-01-15",
      content: "定期清洗过滤网，检查制冷剂压力，更换易损件",
      maintainer: "张师傅",
      cost: 500,
      status: "completed",
      nextMaintenanceDate: "2024-04-15",
      createTime: "2024-01-15 09:00:00",
      createBy: "maintenance"
    },
    {
      recordId: "MR002",
      equipmentId: "EQ001",
      maintenanceType: "repair",
      maintenanceDate: "2023-12-20",
      content: "更换压缩机启动电容，修复温度传感器故障",
      maintainer: "李师傅",
      cost: 800,
      status: "completed",
      createTime: "2023-12-20 14:30:00",
      createBy: "maintenance"
    },
    {
      recordId: "MR003",
      equipmentId: "EQ001",
      maintenanceType: "inspection",
      maintenanceDate: "2023-10-10",
      content: "年度安全检查，性能测试，能效评估",
      maintainer: "王工程师",
      cost: 300,
      status: "completed",
      createTime: "2023-10-10 10:15:00",
      createBy: "maintenance"
    }
  ]
};
