/**
 * 服务器设备模拟数据
 */
import { 
  StatisticCard, 
  TrendChartConfig, 
  ServerDevice, 
  DeviceStatus,
  ChartDataPoint 
} from '../types';

// 服务器设备统计卡片数据
export const serverStatistics: StatisticCard[] = [
  {
    title: '管控设备总数',
    value: 1560,
    trend: {
      type: 'up',
      value: 12,
      period: '月环比'
    }
  },
  {
    title: '服务器在线数量',
    value: 1260,
    chart: {
      type: 'line',
      data: [1100, 1150, 1200, 1180, 1220, 1260, 1240],
      color: '#8B5CF6'
    }
  },
  {
    title: '长期断线服务器数量（超7天）',
    value: 560,
    chart: {
      type: 'bar',
      data: [520, 540, 580, 560, 590, 570, 560],
      color: '#3B82F6'
    }
  },
  {
    title: '长期离线服务器数量（超7天）',
    value: 78,
    trend: {
      type: 'up',
      value: 10,
      period: '占比'
    },
    chart: {
      type: 'line',
      data: [70, 72, 75, 78, 76, 78, 78],
      color: '#06B6D4'
    }
  }
];

// 服务器设备趋势图表数据
const serverChartData: ChartDataPoint[] = [
  { date: '01/01', onlineCount: 200, totalCount: 250 },
  { date: '01/03', onlineCount: 350, totalCount: 400 },
  { date: '01/05', onlineCount: 450, totalCount: 500 },
  { date: '01/07', onlineCount: 400, totalCount: 480 },
  { date: '01/09', onlineCount: 600, totalCount: 650 },
  { date: '01/10', onlineCount: 480, totalCount: 520 },
  { date: '01/11', onlineCount: 520, totalCount: 580 },
  { date: '01/13', onlineCount: 480, totalCount: 520 },
  { date: '01/15', onlineCount: 520, totalCount: 580 },
  { date: '01/17', onlineCount: 600, totalCount: 650 },
  { date: '01/19', onlineCount: 580, totalCount: 620 },
  { date: '01/21', onlineCount: 520, totalCount: 580 },
  { date: '01/23', onlineCount: 580, totalCount: 620 },
  { date: '01/25', onlineCount: 600, totalCount: 650 }
];

// 服务器设备趋势图表配置
export const serverTrendChart: TrendChartConfig = {
  title: '服务器在线趋势',
  data: serverChartData,
  filters: {
    platform: ['安全运维平台', '其他平台'],
    location: ['张三', '李四', '王五'],
    timeRange: ['东莞中心医院', '其他医院']
  }
};

// 服务器设备列表数据
export const serverDeviceList: ServerDevice[] = [
  {
    id: '1',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '张三',
    location: '主机房',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '2',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '李四',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '3',
    status: DeviceStatus.MAINTENANCE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '南五',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '4',
    status: DeviceStatus.FAULT,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '老六',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '5',
    status: DeviceStatus.OFFLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '小七',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  }
];
