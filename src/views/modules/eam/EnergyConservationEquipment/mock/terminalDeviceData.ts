/**
 * 终端设备模拟数据
 */
import { 
  StatisticCard, 
  TrendChartConfig, 
  TerminalDevice, 
  DeviceStatus,
  ChartDataPoint 
} from '../types';

// 终端设备统计卡片数据
export const terminalStatistics: StatisticCard[] = [
  {
    title: '管控终端设备总数',
    value: 1560,
    trend: {
      type: 'up',
      value: 12,
      period: '月环比'
    }
  },
  {
    title: '设备在线数量',
    value: 1260,
    chart: {
      type: 'line',
      data: [1000, 1100, 1200, 1150, 1220, 1260, 1240],
      color: '#8B5CF6'
    }
  },
  {
    title: '长期可疑终端设备数量（超7天）',
    value: 560,
    chart: {
      type: 'bar',
      data: [480, 520, 560, 540, 580, 560, 550],
      color: '#3B82F6'
    }
  },
  {
    title: '长期离线终端设备数量（超7天）',
    value: 78,
    trend: {
      type: 'up',
      value: 10,
      period: '占比'
    },
    chart: {
      type: 'line',
      data: [65, 68, 72, 75, 78, 76, 78],
      color: '#06B6D4'
    }
  }
];

// 终端设备趋势图表数据
const terminalChartData: ChartDataPoint[] = [
  { date: '01/01', onlineCount: 150, totalCount: 200 },
  { date: '01/03', onlineCount: 280, totalCount: 350 },
  { date: '01/05', onlineCount: 380, totalCount: 450 },
  { date: '01/07', onlineCount: 650, totalCount: 750 },
  { date: '01/09', onlineCount: 420, totalCount: 500 },
  { date: '01/10', onlineCount: 320, totalCount: 380 },
  { date: '01/11', onlineCount: 450, totalCount: 520 },
  { date: '01/13', onlineCount: 380, totalCount: 450 },
  { date: '01/15', onlineCount: 480, totalCount: 550 },
  { date: '01/17', onlineCount: 520, totalCount: 600 },
  { date: '01/19', onlineCount: 480, totalCount: 550 },
  { date: '01/21', onlineCount: 420, totalCount: 500 },
  { date: '01/23', onlineCount: 480, totalCount: 550 },
  { date: '01/25', onlineCount: 520, totalCount: 600 }
];

// 终端设备趋势图表配置
export const terminalTrendChart: TrendChartConfig = {
  title: '终端在线趋势',
  data: terminalChartData,
  filters: {
    platform: ['张三', '李四', '王五'],
    location: ['东莞中心医院', '其他医院'],
    timeRange: ['近24小时', '近7天', '近30天']
  }
};

// 终端设备列表数据
export const terminalDeviceList: TerminalDevice[] = [
  {
    id: '1',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '张三',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '2',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '李四',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '3',
    status: DeviceStatus.MAINTENANCE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '南五',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '4',
    status: DeviceStatus.OFFLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '老六',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '5',
    status: DeviceStatus.OFFLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '小七',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  }
];
