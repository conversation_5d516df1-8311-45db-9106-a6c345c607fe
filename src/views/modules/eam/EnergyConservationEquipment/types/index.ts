/**
 * 节能设备管理相关类型定义
 */

// 设备基本信息接口
export interface EquipmentInfo {
  equipmentId: string;
  equipmentName: string;
  equipmentCode: string;
  equipmentType: string;
  categoryId: string;
  categoryName: string;
  deptId: string;
  deptName: string;
  location: string;
  status: 'running' | 'stopped' | 'maintenance' | 'fault';
  energyLevel: 'A' | 'B' | 'C' | 'D';
  ratedPower: number;
  manufacturer: string;
  model: string;
  purchaseDate: string;
  installDate: string;
  warrantyEndDate: string;
  description: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

// 设备分类信息接口
export interface EquipmentCategory {
  id: string;
  categoryName: string;
  parentId: string;
  level: number;
  sort: number;
  description: string;
  equipmentCount?: number;
  children?: EquipmentCategory[];
}

// 组织信息接口
export interface DepartmentInfo {
  deptId: string;
  deptName: string;
  parentId: string;
  level: number;
  sort: number;
  equipmentCount?: number;
  children?: DepartmentInfo[];
}

// 能耗数据接口
export interface EnergyConsumptionData {
  equipmentId: string;
  todayConsumption: number;
  monthConsumption: number;
  yearConsumption: number;
  avgConsumption: number;
  peakPower: number;
  avgPower: number;
  runningHours: number;
  efficiency: number;
}

// 维护记录接口
export interface MaintenanceRecord {
  recordId: string;
  equipmentId: string;
  maintenanceType: 'routine' | 'repair' | 'upgrade' | 'inspection';
  maintenanceDate: string;
  content: string;
  maintainer: string;
  cost: number;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  nextMaintenanceDate?: string;
  attachments?: string[];
  createTime: string;
  createBy: string;
}

// 操作日志接口
export interface OperationLog {
  logId: string;
  equipmentId: string;
  operationType: 'create' | 'update' | 'delete' | 'status_change' | 'maintenance' | 'repair';
  operationTime: string;
  operator: string;
  operatorId: string;
  description: string;
  beforeValue?: any;
  afterValue?: any;
  result: 'success' | 'failure';
  errorMessage?: string;
  ipAddress: string;
  userAgent: string;
}

// 设备统计数据接口
export interface EquipmentStatistics {
  total: number;
  running: number;
  stopped: number;
  maintenance: number;
  fault: number;
  energyLevelA: number;
  energyLevelB: number;
  energyLevelC: number;
  energyLevelD: number;
  totalPower: number;
  totalConsumption: number;
}

// 查询条件接口
export interface QueryCondition {
  conditions?: Array<{
    field: string;
    value: string;
    operator: 'fuzzy' | 'exact' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in';
    fuzzyable?: boolean;
  }>;
  dateRange?: string[] | null;
  categoryId?: string;
  deptId?: string;
  status?: string;
  equipmentType?: string;
  energyLevel?: string;
  pageNum: number;
  pageSize: number;
  headerFilter?: {
    filters: any[];
  };
}

// 分页响应接口
export interface PageResponse<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp: number;
}

// 表格列配置接口
export interface TableColumn {
  hide: boolean;
  prop: string;
  label: string;
  fieldType: string;
  width: string | number;
  unit?: string;
  component?: any;
  filters?: boolean;
  sortable?: boolean;
  fixed?: boolean | 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
  [key: string]: any;
}

// 表格配置接口
export interface TableOption {
  align: string;
  menuAlign: string;
  border: boolean;
  stripe: boolean;
  height?: number;
  rowKey: string;
  column: TableColumn[];
  selection?: boolean;
  addBtn?: boolean;
  editBtn?: boolean;
  delBtn?: boolean;
  menuWidth?: number;
  [key: string]: any;
}

// 筛选标签接口
export interface FilterTag {
  label: string;
  value: string;
  checked: boolean;
}

// 搜索条件接口
export interface SearchCondition {
  categoryId: string;
  deptId: string;
  status: string;
  equipmentType: string;
  energyLevel: string;
  [key: string]: any;
}

// 列搜索条件接口
export interface ColumnCondition {
  value: string | null;
  field: string;
  fuzzyable: boolean;
  operator: string;
}

// 分页配置接口
export interface PaginationConfig {
  align: string;
  total: number;
  currentPage: number;
  pageSize: number;
  pageSizes: number[];
}

// 导出参数接口
export interface ExportParams extends QueryCondition {
  exportType: 'excel' | 'csv' | 'pdf';
  fileName?: string;
  columns?: string[];
}

// 导入结果接口
export interface ImportResult {
  total: number;
  success: number;
  failure: number;
  errors: Array<{
    row: number;
    message: string;
  }>;
}

// 设备健康度接口
export interface EquipmentHealth {
  equipmentId: string;
  healthScore: number;
  healthLevel: 'excellent' | 'good' | 'fair' | 'poor';
  healthColor: string;
  healthLabel: string;
  factors: Array<{
    factor: string;
    score: number;
    weight: number;
    description: string;
  }>;
  suggestions: string[];
  lastEvaluationTime: string;
}

// 能耗趋势数据接口
export interface EnergyTrendData {
  date: string;
  consumption: number;
  power: number;
  efficiency: number;
  cost: number;
}

// 设备告警接口
export interface EquipmentAlert {
  alertId: string;
  equipmentId: string;
  equipmentName: string;
  alertType: 'fault' | 'maintenance' | 'energy' | 'performance';
  alertLevel: 'low' | 'medium' | 'high' | 'critical';
  alertMessage: string;
  alertTime: string;
  status: 'active' | 'acknowledged' | 'resolved';
  handler?: string;
  handleTime?: string;
  handleNote?: string;
}

// 设备配置接口
export interface EquipmentConfig {
  equipmentId: string;
  configKey: string;
  configValue: string;
  configType: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  isEditable: boolean;
  updateTime: string;
  updateBy: string;
}
