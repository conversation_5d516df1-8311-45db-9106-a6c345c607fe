/**
 * 网络设备监控管理相关类型定义
 */

// Tab类型枚举
export enum DeviceTabType {
  SERVER = 'server',      // 服务器设备
  TERMINAL = 'terminal'   // 终端设备
}

// 设备状态枚举
export enum DeviceStatus {
  ONLINE = 'online',      // 在线
  OFFLINE = 'offline',    // 离线
  FAULT = 'fault',        // 故障
  MAINTENANCE = 'maintenance' // 维护中
}

// 统计卡片数据接口
export interface StatisticCard {
  title: string;          // 卡片标题
  value: number;          // 数值
  unit?: string;          // 单位
  trend?: {
    type: 'up' | 'down';  // 趋势方向
    value: number;        // 趋势数值
    period: string;       // 时间周期
  };
  chart?: {
    type: 'line' | 'bar'; // 图表类型
    data: number[];       // 图表数据
    color: string;        // 图表颜色
  };
}

// 趋势图表数据点接口
export interface ChartDataPoint {
  date: string;           // 日期
  onlineCount: number;    // 在线数量
  totalCount: number;     // 总数量
}

// 趋势图表配置接口
export interface TrendChartConfig {
  title: string;          // 图表标题
  data: ChartDataPoint[]; // 图表数据
  filters: {
    platform: string[];   // 平台筛选
    location: string[];   // 地点筛选
    timeRange: string[];  // 时间范围筛选
  };
}

// 服务器设备接口
export interface ServerDevice {
  id: string;             // 设备ID
  status: DeviceStatus;   // 设备状态
  ipAddress: string;      // IP地址
  macAddress: string;     // MAC地址
  businessSystem: string; // 所属业务系统
  assetOwner: string;     // 资产责任人
  location: string;       // 所在位置
  onlineDuration: string; // 自动在线时长
  operations: string[];   // 可执行操作
}

// 终端设备接口
export interface TerminalDevice {
  id: string;             // 设备ID
  status: DeviceStatus;   // 设备状态
  ipAddress: string;      // IP地址
  macAddress: string;     // MAC地址
  assetOwner: string;     // 资产责任人
  location: string;       // 所在位置
  nearestOnlineTime: string; // 近一月自动在线时长
  lastOnlineTime: string; // 最近一次在线时间
  lastOnlineDuration: string; // 最近一次在线时长
  operations: string[];   // 可执行操作
}

// 表格筛选条件接口
export interface TableFilters {
  searchKeyword: string;  // 搜索关键词
  platform?: string;     // 平台筛选
  location?: string;      // 地点筛选
  timeRange?: string;     // 时间范围筛选
}

// 分页信息接口
export interface PaginationInfo {
  current: number;        // 当前页码
  pageSize: number;       // 每页条数
  total: number;          // 总条数
}

// API响应基础接口
export interface ApiResponse<T = any> {
  code: number;           // 响应码
  message: string;        // 响应消息
  data: T;               // 响应数据
  success: boolean;       // 是否成功
}

// 设备列表响应接口
export interface DeviceListResponse<T> {
  list: T[];             // 设备列表
  pagination: PaginationInfo; // 分页信息
}

// Tab配置接口
export interface TabConfig {
  key: DeviceTabType;     // Tab键值
  label: string;          // Tab标签
  statistics: StatisticCard[]; // 统计卡片
  chartConfig: TrendChartConfig; // 图表配置
}

// 主页面状态接口
export interface HomePageState {
  activeTab: DeviceTabType;     // 当前激活的Tab
  serverDevices: ServerDevice[]; // 服务器设备列表
  terminalDevices: TerminalDevice[]; // 终端设备列表
  tableFilters: TableFilters;   // 表格筛选条件
  pagination: PaginationInfo;   // 分页信息
  loading: boolean;             // 加载状态
}

// 设备基本信息接口
export interface EquipmentInfo {
  equipmentId: string;
  equipmentName: string;
  equipmentCode: string;
  equipmentType: string;
  categoryId: string;
  categoryName: string;
  deptId: string;
  deptName: string;
  location: string;
  status: 'running' | 'stopped' | 'maintenance' | 'fault';
  energyLevel: 'A' | 'B' | 'C' | 'D';
  ratedPower: number;
  manufacturer: string;
  model: string;
  purchaseDate: string;
  installDate: string;
  warrantyEndDate: string;
  description: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

// 设备分类信息接口
export interface EquipmentCategory {
  id: string;
  categoryName: string;
  parentId: string;
  level: number;
  sort: number;
  description: string;
  equipmentCount?: number;
  children?: EquipmentCategory[];
}

// 组织信息接口
export interface DepartmentInfo {
  deptId: string;
  deptName: string;
  parentId: string;
  level: number;
  sort: number;
  equipmentCount?: number;
  children?: DepartmentInfo[];
}

// 能耗数据接口
export interface EnergyConsumptionData {
  equipmentId: string;
  todayConsumption: number;
  monthConsumption: number;
  yearConsumption: number;
  avgConsumption: number;
  peakPower: number;
  avgPower: number;
  runningHours: number;
  efficiency: number;
}

// 维护记录接口
export interface MaintenanceRecord {
  recordId: string;
  equipmentId: string;
  maintenanceType: 'routine' | 'repair' | 'upgrade' | 'inspection';
  maintenanceDate: string;
  content: string;
  maintainer: string;
  cost: number;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  nextMaintenanceDate?: string;
  attachments?: string[];
  createTime: string;
  createBy: string;
}

// 操作日志接口
export interface OperationLog {
  logId: string;
  equipmentId: string;
  operationType: 'create' | 'update' | 'delete' | 'status_change' | 'maintenance' | 'repair';
  operationTime: string;
  operator: string;
  operatorId: string;
  description: string;
  beforeValue?: any;
  afterValue?: any;
  result: 'success' | 'failure';
  errorMessage?: string;
  ipAddress: string;
  userAgent: string;
}

// 设备统计数据接口
export interface EquipmentStatistics {
  total: number;
  running: number;
  stopped: number;
  maintenance: number;
  fault: number;
  energyLevelA: number;
  energyLevelB: number;
  energyLevelC: number;
  energyLevelD: number;
  totalPower: number;
  totalConsumption: number;
}

// 查询条件接口
export interface QueryCondition {
  conditions?: Array<{
    field: string;
    value: string;
    operator: 'fuzzy' | 'exact' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in';
    fuzzyable?: boolean;
  }>;
  dateRange?: string[] | null;
  categoryId?: string;
  deptId?: string;
  status?: string;
  equipmentType?: string;
  energyLevel?: string;
  pageNum: number;
  pageSize: number;
  headerFilter?: {
    filters: any[];
  };
}

// 分页响应接口
export interface PageResponse<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp: number;
}

// 表格列配置接口
export interface TableColumn {
  hide: boolean;
  prop: string;
  label: string;
  fieldType: string;
  width: string | number;
  unit?: string;
  component?: any;
  filters?: boolean;
  sortable?: boolean;
  fixed?: boolean | 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
  [key: string]: any;
}

// 表格配置接口
export interface TableOption {
  align: string;
  menuAlign: string;
  border: boolean;
  stripe: boolean;
  height?: number;
  rowKey: string;
  column: TableColumn[];
  selection?: boolean;
  addBtn?: boolean;
  editBtn?: boolean;
  delBtn?: boolean;
  menuWidth?: number;
  [key: string]: any;
}

// 筛选标签接口
export interface FilterTag {
  label: string;
  value: string;
  checked: boolean;
}

// 搜索条件接口
export interface SearchCondition {
  categoryId: string;
  deptId: string;
  status: string;
  equipmentType: string;
  energyLevel: string;
  [key: string]: any;
}

// 列搜索条件接口
export interface ColumnCondition {
  value: string | null;
  field: string;
  fuzzyable: boolean;
  operator: string;
}

// 分页配置接口
export interface PaginationConfig {
  align: string;
  total: number;
  currentPage: number;
  pageSize: number;
  pageSizes: number[];
}

// 导出参数接口
export interface ExportParams extends QueryCondition {
  exportType: 'excel' | 'csv' | 'pdf';
  fileName?: string;
  columns?: string[];
}

// 导入结果接口
export interface ImportResult {
  total: number;
  success: number;
  failure: number;
  errors: Array<{
    row: number;
    message: string;
  }>;
}

// 设备健康度接口
export interface EquipmentHealth {
  equipmentId: string;
  healthScore: number;
  healthLevel: 'excellent' | 'good' | 'fair' | 'poor';
  healthColor: string;
  healthLabel: string;
  factors: Array<{
    factor: string;
    score: number;
    weight: number;
    description: string;
  }>;
  suggestions: string[];
  lastEvaluationTime: string;
}

// 能耗趋势数据接口
export interface EnergyTrendData {
  date: string;
  consumption: number;
  power: number;
  efficiency: number;
  cost: number;
}

// 设备告警接口
export interface EquipmentAlert {
  alertId: string;
  equipmentId: string;
  equipmentName: string;
  alertType: 'fault' | 'maintenance' | 'energy' | 'performance';
  alertLevel: 'low' | 'medium' | 'high' | 'critical';
  alertMessage: string;
  alertTime: string;
  status: 'active' | 'acknowledged' | 'resolved';
  handler?: string;
  handleTime?: string;
  handleNote?: string;
}

// 设备配置接口
export interface EquipmentConfig {
  equipmentId: string;
  configKey: string;
  configValue: string;
  configType: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  isEditable: boolean;
  updateTime: string;
  updateBy: string;
}
