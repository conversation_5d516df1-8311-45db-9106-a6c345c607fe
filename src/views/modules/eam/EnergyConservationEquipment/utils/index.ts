/**
 * 节能设备管理工具方法
 */

// 设备状态枚举
export enum EquipmentStatus {
  RUNNING = 'running',
  STOPPED = 'stopped',
  MAINTENANCE = 'maintenance',
  FAULT = 'fault'
}

// 设备类型枚举
export enum EquipmentType {
  AIR_CONDITIONING = 'air_conditioning',
  LIGHTING = 'lighting',
  HEATING = 'heating',
  VENTILATION = 'ventilation',
  OTHER = 'other'
}

// 能耗等级枚举
export enum EnergyLevel {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D'
}

/**
 * 获取设备状态类型（用于Element UI Tag组件）
 * @param status 设备状态
 * @returns Element UI Tag类型
 */
export const getEquipmentStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [EquipmentStatus.RUNNING]: 'success',
    [EquipmentStatus.STOPPED]: 'info',
    [EquipmentStatus.MAINTENANCE]: 'warning',
    [EquipmentStatus.FAULT]: 'danger'
  };
  return statusMap[status] || 'info';
};

/**
 * 获取设备状态标签文本
 * @param status 设备状态
 * @returns 状态标签文本
 */
export const getEquipmentStatusLabel = (status: string): string => {
  const statusMap: Record<string, string> = {
    [EquipmentStatus.RUNNING]: '运行中',
    [EquipmentStatus.STOPPED]: '停机',
    [EquipmentStatus.MAINTENANCE]: '维护中',
    [EquipmentStatus.FAULT]: '故障'
  };
  return statusMap[status] || '未知';
};

/**
 * 获取设备类型标签文本
 * @param type 设备类型
 * @returns 类型标签文本
 */
export const getEquipmentTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    [EquipmentType.AIR_CONDITIONING]: '空调设备',
    [EquipmentType.LIGHTING]: '照明设备',
    [EquipmentType.HEATING]: '供暖设备',
    [EquipmentType.VENTILATION]: '通风设备',
    [EquipmentType.OTHER]: '其他设备'
  };
  return typeMap[type] || '未知类型';
};

/**
 * 获取能耗等级颜色
 * @param level 能耗等级
 * @returns 颜色值
 */
export const getEnergyLevelColor = (level: string): string => {
  const colorMap: Record<string, string> = {
    [EnergyLevel.A]: '#67C23A', // 绿色 - 优秀
    [EnergyLevel.B]: '#E6A23C', // 橙色 - 良好
    [EnergyLevel.C]: '#F56C6C', // 红色 - 一般
    [EnergyLevel.D]: '#909399'  // 灰色 - 较差
  };
  return colorMap[level] || '#909399';
};

/**
 * 获取能耗等级标签文本
 * @param level 能耗等级
 * @returns 等级标签文本
 */
export const getEnergyLevelLabel = (level: string): string => {
  const labelMap: Record<string, string> = {
    [EnergyLevel.A]: 'A级(优秀)',
    [EnergyLevel.B]: 'B级(良好)',
    [EnergyLevel.C]: 'C级(一般)',
    [EnergyLevel.D]: 'D级(较差)'
  };
  return labelMap[level] || '未评级';
};

/**
 * 格式化功率显示
 * @param power 功率值（kW）
 * @returns 格式化后的功率字符串
 */
export const formatPower = (power: number): string => {
  if (!power) return '0 kW';
  
  if (power >= 1000) {
    return `${(power / 1000).toFixed(1)} MW`;
  } else if (power >= 1) {
    return `${power.toFixed(1)} kW`;
  } else {
    return `${(power * 1000).toFixed(0)} W`;
  }
};

/**
 * 格式化能耗显示
 * @param consumption 能耗值（kWh）
 * @returns 格式化后的能耗字符串
 */
export const formatEnergyConsumption = (consumption: number): string => {
  if (!consumption) return '0 kWh';
  
  if (consumption >= 1000000) {
    return `${(consumption / 1000000).toFixed(2)} GWh`;
  } else if (consumption >= 1000) {
    return `${(consumption / 1000).toFixed(2)} MWh`;
  } else {
    return `${consumption.toFixed(2)} kWh`;
  }
};

/**
 * 计算设备运行时长
 * @param startTime 开始时间
 * @param endTime 结束时间（可选，默认为当前时间）
 * @returns 运行时长描述
 */
export const calculateRunningTime = (startTime: string, endTime?: string): string => {
  if (!startTime) return '未知';
  
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  const diffMs = end.getTime() - start.getTime();
  
  if (diffMs < 0) return '未知';
  
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  if (diffDays > 0) {
    return `${diffDays}天${diffHours}小时`;
  } else if (diffHours > 0) {
    return `${diffHours}小时${diffMinutes}分钟`;
  } else {
    return `${diffMinutes}分钟`;
  }
};

/**
 * 验证设备编号格式
 * @param code 设备编号
 * @returns 是否有效
 */
export const validateEquipmentCode = (code: string): boolean => {
  if (!code) return false;
  
  // 设备编号格式：字母开头，后跟数字和字母，长度6-20位
  const codeRegex = /^[A-Z][A-Z0-9]{5,19}$/;
  return codeRegex.test(code.toUpperCase());
};

/**
 * 生成设备编号
 * @param type 设备类型
 * @param sequence 序号
 * @returns 生成的设备编号
 */
export const generateEquipmentCode = (type: string, sequence: number): string => {
  const typePrefix: Record<string, string> = {
    [EquipmentType.AIR_CONDITIONING]: 'AC',
    [EquipmentType.LIGHTING]: 'LT',
    [EquipmentType.HEATING]: 'HT',
    [EquipmentType.VENTILATION]: 'VT',
    [EquipmentType.OTHER]: 'OT'
  };
  
  const prefix = typePrefix[type] || 'EQ';
  const year = new Date().getFullYear().toString().slice(-2);
  const seqStr = sequence.toString().padStart(4, '0');
  
  return `${prefix}${year}${seqStr}`;
};

/**
 * 获取设备健康度评分
 * @param equipment 设备信息
 * @returns 健康度评分（0-100）
 */
export const calculateEquipmentHealthScore = (equipment: any): number => {
  let score = 100;
  
  // 根据设备状态扣分
  switch (equipment.status) {
    case EquipmentStatus.FAULT:
      score -= 50;
      break;
    case EquipmentStatus.MAINTENANCE:
      score -= 20;
      break;
    case EquipmentStatus.STOPPED:
      score -= 10;
      break;
  }
  
  // 根据使用年限扣分
  if (equipment.installDate) {
    const installYear = new Date(equipment.installDate).getFullYear();
    const currentYear = new Date().getFullYear();
    const usageYears = currentYear - installYear;
    
    if (usageYears > 10) {
      score -= (usageYears - 10) * 5;
    }
  }
  
  // 根据能耗等级扣分
  switch (equipment.energyLevel) {
    case EnergyLevel.D:
      score -= 20;
      break;
    case EnergyLevel.C:
      score -= 10;
      break;
    case EnergyLevel.B:
      score -= 5;
      break;
  }
  
  return Math.max(0, Math.min(100, score));
};

/**
 * 获取健康度等级
 * @param score 健康度评分
 * @returns 健康度等级
 */
export const getHealthLevel = (score: number): { level: string; color: string; label: string } => {
  if (score >= 90) {
    return { level: 'excellent', color: '#67C23A', label: '优秀' };
  } else if (score >= 80) {
    return { level: 'good', color: '#409EFF', label: '良好' };
  } else if (score >= 60) {
    return { level: 'fair', color: '#E6A23C', label: '一般' };
  } else {
    return { level: 'poor', color: '#F56C6C', label: '较差' };
  }
};
